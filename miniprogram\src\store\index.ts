import { User, CartItem } from '../types';

// 全局状态接口
interface GlobalState {
  user: User | null;
  cart: CartItem[];
  loading: boolean;
  token: string | null;
  refreshToken: string | null;
}

// 状态管理类
class Store {
  private state: GlobalState = {
    user: null,
    cart: [],
    loading: false,
    token: null,
    refreshToken: null,
  };

  private listeners: Array<(state: GlobalState) => void> = [];

  constructor() {
    this.initFromStorage();
  }

  // 从本地存储初始化状态
  private initFromStorage() {
    try {
      const token = wx.getStorageSync('token');
      const refreshToken = wx.getStorageSync('refreshToken');
      const user = wx.getStorageSync('user');
      const cart = wx.getStorageSync('cart') || [];

      this.state = {
        ...this.state,
        token,
        refreshToken,
        user: user ? JSON.parse(user) : null,
        cart: Array.isArray(cart) ? cart : [],
      };
    } catch (error) {
      console.error('Failed to init state from storage:', error);
    }
  }

  // 获取当前状态
  getState(): GlobalState {
    return { ...this.state };
  }

  // 订阅状态变化
  subscribe(listener: (state: GlobalState) => void) {
    this.listeners.push(listener);
    
    // 返回取消订阅函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // 通知所有监听器
  private notify() {
    this.listeners.forEach(listener => {
      try {
        listener(this.getState());
      } catch (error) {
        console.error('Error in state listener:', error);
      }
    });
  }

  // 更新状态
  private setState(updates: Partial<GlobalState>) {
    this.state = { ...this.state, ...updates };
    this.notify();
  }

  // 用户相关操作
  setUser(user: User | null) {
    this.setState({ user });
    
    if (user) {
      wx.setStorageSync('user', JSON.stringify(user));
    } else {
      wx.removeStorageSync('user');
    }
  }

  getUser(): User | null {
    return this.state.user;
  }

  // Token相关操作
  setTokens(token: string | null, refreshToken: string | null) {
    this.setState({ token, refreshToken });
    
    if (token && refreshToken) {
      wx.setStorageSync('token', token);
      wx.setStorageSync('refreshToken', refreshToken);
    } else {
      wx.removeStorageSync('token');
      wx.removeStorageSync('refreshToken');
    }
  }

  getToken(): string | null {
    return this.state.token;
  }

  // 登录
  login(user: User, token: string, refreshToken: string) {
    this.setUser(user);
    this.setTokens(token, refreshToken);
  }

  // 登出
  logout() {
    this.setUser(null);
    this.setTokens(null, null);
    this.setCart([]);
  }

  // 购物车相关操作
  setCart(cart: CartItem[]) {
    this.setState({ cart });
    wx.setStorageSync('cart', cart);
  }

  getCart(): CartItem[] {
    return this.state.cart;
  }

  // 添加到购物车
  addToCart(item: CartItem) {
    const cart = [...this.state.cart];
    const existingIndex = cart.findIndex(
      cartItem => cartItem.variant_id === item.variant_id
    );

    if (existingIndex > -1) {
      // 如果商品已存在，增加数量
      cart[existingIndex].quantity += item.quantity;
    } else {
      // 如果商品不存在，添加新项
      cart.push(item);
    }

    this.setCart(cart);
  }

  // 更新购物车项数量
  updateCartItemQuantity(variantId: string, quantity: number) {
    const cart = this.state.cart.map(item => 
      item.variant_id === variantId 
        ? { ...item, quantity: Math.max(0, quantity) }
        : item
    ).filter(item => item.quantity > 0);

    this.setCart(cart);
  }

  // 删除购物车项
  removeCartItem(variantId: string) {
    const cart = this.state.cart.filter(item => item.variant_id !== variantId);
    this.setCart(cart);
  }

  // 清空购物车
  clearCart() {
    this.setCart([]);
  }

  // 切换购物车项选中状态
  toggleCartItemSelected(variantId: string, selected?: boolean) {
    const cart = this.state.cart.map(item => 
      item.variant_id === variantId 
        ? { ...item, selected: selected !== undefined ? selected : !item.selected }
        : item
    );

    this.setCart(cart);
  }

  // 全选/取消全选购物车
  toggleAllCartItems(selected: boolean) {
    const cart = this.state.cart.map(item => ({ ...item, selected }));
    this.setCart(cart);
  }

  // 获取选中的购物车项
  getSelectedCartItems(): CartItem[] {
    return this.state.cart.filter(item => item.selected);
  }

  // 获取购物车总数量
  getCartTotalQuantity(): number {
    return this.state.cart.reduce((total, item) => total + item.quantity, 0);
  }

  // 获取选中商品总价
  getSelectedCartTotal(): number {
    return this.state.cart
      .filter(item => item.selected)
      .reduce((total, item) => total + item.price * item.quantity, 0);
  }

  // 加载状态相关操作
  setLoading(loading: boolean) {
    this.setState({ loading });
  }

  getLoading(): boolean {
    return this.state.loading;
  }

  // 合并本地购物车到服务器（登录后调用）
  async mergeCartToServer() {
    if (!this.state.user || this.state.cart.length === 0) {
      return;
    }

    try {
      const { cartApi } = await import('../services/api');
      const items = this.state.cart.map(item => ({
        variant_id: item.variant_id,
        quantity: item.quantity,
      }));

      await cartApi.mergeCart(items);
      
      // 合并成功后，从服务器重新获取购物车
      const response = await cartApi.getCart();
      this.setCart(response.data);
    } catch (error) {
      console.error('Failed to merge cart:', error);
    }
  }

  // 从服务器同步购物车
  async syncCartFromServer() {
    if (!this.state.user) {
      return;
    }

    try {
      const { cartApi } = await import('../services/api');
      const response = await cartApi.getCart();
      this.setCart(response.data);
    } catch (error) {
      console.error('Failed to sync cart from server:', error);
    }
  }
}

// 创建全局store实例
export const store = new Store();

// 导出便捷的hook函数
export const useStore = () => {
  return {
    getState: () => store.getState(),
    subscribe: (listener: (state: GlobalState) => void) => store.subscribe(listener),
    
    // 用户相关
    setUser: (user: User | null) => store.setUser(user),
    getUser: () => store.getUser(),
    login: (user: User, token: string, refreshToken: string) => store.login(user, token, refreshToken),
    logout: () => store.logout(),
    
    // 购物车相关
    getCart: () => store.getCart(),
    addToCart: (item: CartItem) => store.addToCart(item),
    updateCartItemQuantity: (variantId: string, quantity: number) => store.updateCartItemQuantity(variantId, quantity),
    removeCartItem: (variantId: string) => store.removeCartItem(variantId),
    clearCart: () => store.clearCart(),
    toggleCartItemSelected: (variantId: string, selected?: boolean) => store.toggleCartItemSelected(variantId, selected),
    toggleAllCartItems: (selected: boolean) => store.toggleAllCartItems(selected),
    getSelectedCartItems: () => store.getSelectedCartItems(),
    getCartTotalQuantity: () => store.getCartTotalQuantity(),
    getSelectedCartTotal: () => store.getSelectedCartTotal(),
    
    // 加载状态
    setLoading: (loading: boolean) => store.setLoading(loading),
    getLoading: () => store.getLoading(),
    
    // 同步相关
    mergeCartToServer: () => store.mergeCartToServer(),
    syncCartFromServer: () => store.syncCartFromServer(),
  };
};

export default store;

<view class="category-page">
  <view class="category-container">
    <!-- 左侧分类列表 -->
    <scroll-view class="category-sidebar" scroll-y="{{true}}">
      <view 
        wx:for="{{categories}}" 
        wx:key="id"
        class="category-item {{currentCategoryId === item.id ? 'active' : ''}}"
        bindtap="onCategoryTap"
        data-category="{{item}}"
      >
        <view class="category-name">{{item.name}}</view>
      </view>
    </scroll-view>

    <!-- 右侧商品列表 -->
    <scroll-view 
      class="product-content" 
      scroll-y="{{true}}"
      bindscrolltolower="onLoadMore"
      refresher-enabled="{{true}}"
      refresher-triggered="{{refreshing}}"
      bindrefresherrefresh="onRefresh"
    >
      <!-- 分类信息 -->
      <view wx:if="{{currentCategory}}" class="category-header">
        <view class="category-title">{{currentCategory.name}}</view>
        <view class="product-count">共{{totalCount}}件商品</view>
      </view>

      <!-- 排序筛选 -->
      <view class="filter-bar">
        <view class="filter-tabs">
          <view 
            wx:for="{{sortOptions}}" 
            wx:key="value"
            class="filter-tab {{currentSort === item.value ? 'active' : ''}}"
            bindtap="onSortTap"
            data-sort="{{item.value}}"
          >
            {{item.label}}
          </view>
        </view>
        <view class="layout-switch" bindtap="onLayoutSwitch">
          <text class="layout-icon">{{layout === 'grid' ? '☰' : '⊞'}}</text>
        </view>
      </view>

      <!-- 商品列表 -->
      <view class="product-list {{layout === 'grid' ? 'grid-layout' : 'list-layout'}}">
        <product-card
          wx:for="{{products}}"
          wx:key="id"
          product="{{item}}"
          layout="{{layout}}"
          bind:tap="onProductTap"
          bind:addToCart="onAddToCart"
        />
      </view>

      <!-- 加载状态 -->
      <view wx:if="{{loading}}" class="loading-more">
        <text class="loading-icon">⏳</text>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 没有更多数据 -->
      <view wx:elif="{{!hasMore && products.length > 0}}" class="no-more">
        没有更多商品了
      </view>

      <!-- 空状态 -->
      <view wx:elif="{{products.length === 0 && !loading}}" class="empty-state">
        <view class="empty-icon">📦</view>
        <view class="empty-text">暂无商品</view>
        <view class="empty-desc">该分类下还没有商品</view>
      </view>

      <!-- 底部占位 -->
      <view class="bottom-placeholder"></view>
    </scroll-view>
  </view>
</view>

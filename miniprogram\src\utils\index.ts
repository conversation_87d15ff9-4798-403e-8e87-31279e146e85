// 格式化价格
export const formatPrice = (price: number): string => {
  return `¥${(price / 100).toFixed(2)}`;
};

// 格式化数字（添加千分位分隔符）
export const formatNumber = (num: number): string => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 格式化时间
export const formatTime = (timestamp: string | number, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  const date = new Date(timestamp);
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', year.toString())
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

// 相对时间格式化
export const formatRelativeTime = (timestamp: string | number): string => {
  const now = Date.now();
  const time = new Date(timestamp).getTime();
  const diff = now - time;

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;
  const month = 30 * day;

  if (diff < minute) {
    return '刚刚';
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`;
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`;
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`;
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`;
  } else {
    return formatTime(timestamp, 'YYYY-MM-DD');
  }
};

// 生成UUID
export const generateUUID = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: number | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(() => {
      func(...args);
    }, wait);
  };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let lastTime = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    
    if (now - lastTime >= wait) {
      lastTime = now;
      func(...args);
    }
  };
};

// 深拷贝
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
};

// 验证手机号
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

// 验证邮箱
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// 验证身份证号
export const validateIdCard = (idCard: string): boolean => {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return idCardRegex.test(idCard);
};

// 获取图片信息
export const getImageInfo = (src: string): Promise<wx.GetImageInfoSuccessCallbackResult> => {
  return new Promise((resolve, reject) => {
    wx.getImageInfo({
      src,
      success: resolve,
      fail: reject,
    });
  });
};

// 压缩图片
export const compressImage = async (
  src: string, 
  quality: number = 0.8,
  maxWidth: number = 1200
): Promise<string> => {
  try {
    const imageInfo = await getImageInfo(src);
    const { width, height } = imageInfo;
    
    // 如果图片尺寸已经很小，直接返回
    if (width <= maxWidth) {
      return src;
    }
    
    // 计算压缩后的尺寸
    const ratio = maxWidth / width;
    const newWidth = maxWidth;
    const newHeight = Math.floor(height * ratio);
    
    return new Promise((resolve, reject) => {
      const canvas = wx.createCanvasContext('compress-canvas');
      
      canvas.drawImage(src, 0, 0, newWidth, newHeight);
      canvas.draw(false, () => {
        wx.canvasToTempFilePath({
          canvasId: 'compress-canvas',
          width: newWidth,
          height: newHeight,
          destWidth: newWidth,
          destHeight: newHeight,
          quality,
          success: (res) => resolve(res.tempFilePath),
          fail: reject,
        });
      });
    });
  } catch (error) {
    console.error('Image compression failed:', error);
    return src; // 压缩失败时返回原图
  }
};

// 选择图片
export const chooseImage = (count: number = 1): Promise<string[]> => {
  return new Promise((resolve, reject) => {
    wx.chooseImage({
      count,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => resolve(res.tempFilePaths),
      fail: reject,
    });
  });
};

// 预览图片
export const previewImage = (current: string, urls: string[]) => {
  wx.previewImage({
    current,
    urls,
  });
};

// 复制到剪贴板
export const copyToClipboard = (data: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    wx.setClipboardData({
      data,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success',
        });
        resolve();
      },
      fail: reject,
    });
  });
};

// 拨打电话
export const makePhoneCall = (phoneNumber: string) => {
  wx.makePhoneCall({
    phoneNumber,
    fail: (error) => {
      console.error('Failed to make phone call:', error);
      wx.showToast({
        title: '拨号失败',
        icon: 'none',
      });
    },
  });
};

// 获取系统信息
export const getSystemInfo = (): Promise<wx.GetSystemInfoSuccessCallbackResult> => {
  return new Promise((resolve, reject) => {
    wx.getSystemInfo({
      success: resolve,
      fail: reject,
    });
  });
};

// 显示加载提示
export const showLoading = (title: string = '加载中...', mask: boolean = true) => {
  wx.showLoading({
    title,
    mask,
  });
};

// 隐藏加载提示
export const hideLoading = () => {
  wx.hideLoading();
};

// 显示成功提示
export const showSuccess = (title: string, duration: number = 2000) => {
  wx.showToast({
    title,
    icon: 'success',
    duration,
  });
};

// 显示错误提示
export const showError = (title: string, duration: number = 2000) => {
  wx.showToast({
    title,
    icon: 'none',
    duration,
  });
};

// 显示确认对话框
export const showConfirm = (
  title: string, 
  content: string = ''
): Promise<boolean> => {
  return new Promise((resolve) => {
    wx.showModal({
      title,
      content,
      success: (res) => resolve(res.confirm),
      fail: () => resolve(false),
    });
  });
};

// 页面跳转
export const navigateTo = (url: string) => {
  wx.navigateTo({
    url,
    fail: (error) => {
      console.error('Navigation failed:', error);
    },
  });
};

// 页面重定向
export const redirectTo = (url: string) => {
  wx.redirectTo({
    url,
    fail: (error) => {
      console.error('Redirect failed:', error);
    },
  });
};

// 返回上一页
export const navigateBack = (delta: number = 1) => {
  wx.navigateBack({
    delta,
    fail: (error) => {
      console.error('Navigate back failed:', error);
    },
  });
};

// 重启到指定页面
export const reLaunch = (url: string) => {
  wx.reLaunch({
    url,
    fail: (error) => {
      console.error('Relaunch failed:', error);
    },
  });
};

// 切换到tabBar页面
export const switchTab = (url: string) => {
  wx.switchTab({
    url,
    fail: (error) => {
      console.error('Switch tab failed:', error);
    },
  });
};

// 获取当前页面栈
export const getCurrentPages = () => {
  return getCurrentPages();
};

// 安全的JSON解析
export const safeJsonParse = <T = any>(str: string, defaultValue: T): T => {
  try {
    return JSON.parse(str);
  } catch (error) {
    console.error('JSON parse error:', error);
    return defaultValue;
  }
};

// 安全的JSON字符串化
export const safeJsonStringify = (obj: any, defaultValue: string = '{}'): string => {
  try {
    return JSON.stringify(obj);
  } catch (error) {
    console.error('JSON stringify error:', error);
    return defaultValue;
  }
};

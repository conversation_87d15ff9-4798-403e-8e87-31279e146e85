.category-page {
  height: 100vh;
  background-color: #F7F9FA;
}

.category-container {
  display: flex;
  height: 100%;
}

/* 左侧分类列表 */
.category-sidebar {
  width: 200rpx;
  background-color: #FFFFFF;
  border-right: 1rpx solid #E5E5E5;
}

.category-item {
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #F5F5F5;
  text-align: center;
  position: relative;
}

.category-item.active {
  background-color: #F8FFF9;
  color: #2FA85A;
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background-color: #2FA85A;
}

.category-name {
  font-size: 28rpx;
  line-height: 1.4;
  word-break: break-all;
}

/* 右侧商品内容 */
.product-content {
  flex: 1;
  background-color: #F7F9FA;
}

/* 分类头部 */
.category-header {
  background-color: #FFFFFF;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5E5;
}

.category-title {
  font-size: 36rpx;
  color: #333333;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.product-count {
  font-size: 24rpx;
  color: #666666;
}

/* 筛选栏 */
.filter-bar {
  background-color: #FFFFFF;
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #E5E5E5;
}

.filter-tabs {
  display: flex;
  gap: 48rpx;
}

.filter-tab {
  font-size: 28rpx;
  color: #666666;
  position: relative;
  padding-bottom: 8rpx;
}

.filter-tab.active {
  color: #2FA85A;
  font-weight: 500;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #2FA85A;
  border-radius: 2rpx;
}

.layout-switch {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F5F5F5;
  border-radius: 8rpx;
}

.layout-icon {
  font-size: 32rpx;
  color: #666666;
}

/* 商品列表 */
.product-list {
  padding: 32rpx;
}

.product-list.grid-layout {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.product-list.list-layout {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 加载状态 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  gap: 16rpx;
}

.loading-icon {
  font-size: 32rpx;
  animation: rotate 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

.no-more {
  text-align: center;
  padding: 32rpx;
  font-size: 28rpx;
  color: #999999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 128rpx 32rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 36rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 28rpx;
  color: #666666;
}

/* 底部占位 */
.bottom-placeholder {
  height: 32rpx;
}

/* 动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .category-sidebar {
    width: 160rpx;
  }
  
  .category-item {
    padding: 24rpx 16rpx;
  }
  
  .category-name {
    font-size: 24rpx;
  }
  
  .filter-tabs {
    gap: 32rpx;
  }
  
  .filter-tab {
    font-size: 24rpx;
  }
}

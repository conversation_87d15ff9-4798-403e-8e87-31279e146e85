.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #2FA85A 0%, #1E7E34 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 64rpx 32rpx;
}

.login-container {
  width: 100%;
  max-width: 640rpx;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 96rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  background-color: #FFFFFF;
  border-radius: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.logo-text {
  font-size: 48rpx;
  font-weight: 700;
  color: #2FA85A;
}

.subtitle {
  font-size: 32rpx;
  color: #FFFFFF;
  opacity: 0.9;
}

/* 登录表单 */
.login-form {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 64rpx 48rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 48rpx;
}

.input-group {
  margin-bottom: 48rpx;
}

.input-label {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input-field {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #E5E5E5;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  color: #333333;
  background-color: #FFFFFF;
}

.input-field:focus {
  border-color: #2FA85A;
}

.input-field::placeholder {
  color: #999999;
}

/* 验证码输入 */
.code-input-container {
  display: flex;
  gap: 16rpx;
}

.code-input {
  flex: 1;
}

.send-code-btn {
  width: 200rpx;
  height: 88rpx;
  background-color: #2FA85A;
  color: #FFFFFF;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-code-btn.disabled {
  background-color: #CCCCCC;
  color: #999999;
}

.send-code-btn::after {
  border: none;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 96rpx;
  background-color: #2FA85A;
  color: #FFFFFF;
  border: none;
  border-radius: 8rpx;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 32rpx;
}

.login-btn.disabled {
  background-color: #CCCCCC;
  color: #999999;
}

.login-btn::after {
  border: none;
}

/* 协议同意 */
.agreement {
  margin-bottom: 32rpx;
}

.agreement-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
  flex: 1;
}

.link {
  color: #2FA85A;
  text-decoration: underline;
}

/* 其他登录方式 */
.other-login {
  margin-bottom: 48rpx;
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background-color: rgba(255, 255, 255, 0.3);
}

.divider-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 32rpx;
}

.social-login {
  display: flex;
  justify-content: center;
}

.social-btn {
  background-color: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.social-btn::after {
  border: none;
}

.social-icon {
  font-size: 32rpx;
}

.social-text {
  font-size: 28rpx;
  color: #FFFFFF;
}

/* 底部提示 */
.footer-tips {
  text-align: center;
}

.tip-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 16rpx;
}

.tip-text:last-child {
  margin-bottom: 0;
}

/* 动画效果 */
.login-form {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式调整 */
@media (max-height: 1200rpx) {
  .login-page {
    padding: 32rpx;
  }
  
  .header {
    margin-bottom: 64rpx;
  }
  
  .logo {
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
  }
  
  .logo-text {
    font-size: 36rpx;
  }
  
  .login-form {
    padding: 48rpx 32rpx;
  }
}

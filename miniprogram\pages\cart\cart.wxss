.cart-page {
  min-height: 100vh;
  background-color: #F7F9FA;
  padding-bottom: 120rpx; /* 为底部结算栏留出空间 */
}

/* 空购物车状态 */
.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
  padding: 64rpx 32rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 36rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 48rpx;
}

.go-shopping-btn {
  width: 320rpx;
  height: 80rpx;
  background-color: #2FA85A;
  color: #FFFFFF;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
}

.go-shopping-btn::after {
  border: none;
}

/* 购物车内容 */
.cart-content {
  padding: 0 0 32rpx 0;
}

/* 全选栏 */
.select-all-bar {
  background-color: #FFFFFF;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #E5E5E5;
}

.select-all-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.select-all-text {
  font-size: 32rpx;
  color: #333333;
}

.cart-count {
  font-size: 28rpx;
  color: #666666;
}

/* 购物车列表 */
.cart-list {
  background-color: #FFFFFF;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F5F5F5;
  position: relative;
}

.cart-item.selected {
  background-color: #F8FFF9;
}

.item-checkbox {
  margin-right: 24rpx;
}

.item-content {
  flex: 1;
  display: flex;
  gap: 24rpx;
}

.item-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  background-color: #F5F5F5;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-name {
  font-size: 32rpx;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.item-variant {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 16rpx;
}

.item-price {
  font-size: 36rpx;
  color: #2FA85A;
  font-weight: 600;
}

.stock-warning {
  font-size: 24rpx;
  color: #FF4757;
  margin-top: 8rpx;
}

/* 数量控制 */
.quantity-control {
  display: flex;
  align-items: center;
  margin: 0 24rpx;
}

.quantity-btn {
  width: 64rpx;
  height: 64rpx;
  border: 1rpx solid #E5E5E5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #333333;
  background-color: #FFFFFF;
}

.quantity-btn.decrease {
  border-radius: 8rpx 0 0 8rpx;
}

.quantity-btn.increase {
  border-radius: 0 8rpx 8rpx 0;
}

.quantity-btn.disabled {
  color: #CCCCCC;
  background-color: #F5F5F5;
}

.quantity-input {
  width: 80rpx;
  height: 64rpx;
  border: 1rpx solid #E5E5E5;
  border-left: none;
  border-right: none;
  text-align: center;
  font-size: 28rpx;
  background-color: #FFFFFF;
}

/* 删除按钮 */
.delete-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #FF4757;
}

/* 推荐商品 */
.recommend-section {
  background-color: #FFFFFF;
  margin-top: 16rpx;
  padding: 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

.recommend-list {
  white-space: nowrap;
}

.recommend-products {
  display: inline-flex;
  gap: 24rpx;
}

.recommend-item {
  width: 200rpx;
  flex-shrink: 0;
}

.recommend-image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  background-color: #F5F5F5;
  margin-bottom: 12rpx;
}

.recommend-name {
  font-size: 24rpx;
  color: #333333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommend-price {
  font-size: 28rpx;
  color: #2FA85A;
  font-weight: 600;
}

/* 底部结算栏 */
.checkout-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-top: 1rpx solid #E5E5E5;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
}

.checkout-info {
  flex: 1;
}

.selected-count {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.total-price {
  font-size: 28rpx;
  color: #333333;
}

.price-value {
  font-size: 36rpx;
  color: #2FA85A;
  font-weight: 600;
}

.checkout-btn {
  width: 200rpx;
  height: 80rpx;
  background-color: #2FA85A;
  color: #FFFFFF;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.checkout-btn.disabled {
  background-color: #CCCCCC;
  color: #999999;
}

.checkout-btn::after {
  border: none;
}

/* 滑动删除效果 */
.cart-item {
  transition: all 0.3s ease;
}

.cart-item.deleting {
  transform: translateX(-100%);
  opacity: 0;
}

.image-carousel {
  position: relative;
  width: 100%;
  height: 100%;
}

.carousel-swiper {
  width: 100%;
  height: 100%;
}

.carousel-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.carousel-image {
  width: 100%;
  height: 100%;
  background-color: #F5F5F5;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #F5F5F5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
  opacity: 0.3;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999999;
}

/* 自定义指示器 */
.custom-dots {
  position: absolute;
  bottom: 32rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 24rpx;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 32rpx;
}

.custom-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.custom-dot.active {
  background-color: #FFFFFF;
  transform: scale(1.2);
}

/* 图片计数 */
.image-counter {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  padding: 8rpx 16rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #FFFFFF;
  font-size: 24rpx;
  border-radius: 16rpx;
}

/* 不同尺寸样式 */
.image-carousel.small {
  height: 200rpx;
}

.image-carousel.medium {
  height: 400rpx;
}

.image-carousel.large {
  height: 600rpx;
}

.image-carousel.square {
  height: 100vw;
}

/* 圆角样式 */
.image-carousel.rounded {
  border-radius: 8rpx;
  overflow: hidden;
}

.image-carousel.rounded-lg {
  border-radius: 16rpx;
  overflow: hidden;
}

/* 阴影样式 */
.image-carousel.shadow {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.image-carousel.shadow-lg {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

/* 边框样式 */
.image-carousel.border {
  border: 1rpx solid #E5E5E5;
}

/* 加载动画 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.image-placeholder {
  animation: pulse 1.5s ease-in-out infinite;
}

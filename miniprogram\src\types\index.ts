// 用户相关类型
export interface User {
  id: string;
  phone: string;
  nickname?: string;
  avatar?: string;
  role: 'C' | 'B'; // C端用户或B端用户
  status: 'active' | 'inactive';
  license_status?: 'none' | 'uploaded' | 'under_review' | 'approved' | 'rejected';
  license_reject_reason?: string;
  api_token?: string;
  created_at: string;
  updated_at: string;
}

// 商品相关类型
export interface Product {
  id: string;
  name: string;
  description: string;
  images: string[];
  category_id: string;
  category_name: string;
  price_c: number; // C端价格
  price_b: number; // B端价格
  original_price?: number;
  stock: number;
  sales: number;
  status: 'active' | 'inactive';
  variants: ProductVariant[];
  created_at: string;
  updated_at: string;
}

export interface ProductVariant {
  id: string;
  product_id: string;
  name: string;
  price_c: number;
  price_b: number;
  stock: number;
  sku: string;
  attributes: Record<string, string>; // 规格属性，如 {"颜色": "红色", "尺寸": "L"}
}

// 分类类型
export interface Category {
  id: string;
  name: string;
  icon?: string;
  sort_order: number;
  parent_id?: string;
  children?: Category[];
}

// 购物车相关类型
export interface CartItem {
  id: string;
  product_id: string;
  variant_id: string;
  product_name: string;
  variant_name: string;
  image: string;
  price: number; // 根据用户角色显示的价格
  quantity: number;
  stock: number;
  selected: boolean;
}

// 地址相关类型
export interface Address {
  id: string;
  user_id: string;
  name: string;
  phone: string;
  province: string;
  city: string;
  district: string;
  detail: string;
  is_default: boolean;
  created_at: string;
}

// 订单相关类型
export interface Order {
  id: string;
  order_no: string;
  user_id: string;
  status: 'pending' | 'paid' | 'shipped' | 'delivered' | 'cancelled';
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  total_amount: number;
  shipping_fee: number;
  discount_amount: number;
  final_amount: number;
  address: Address;
  items: OrderItem[];
  payment_method?: string;
  payment_time?: string;
  shipping_time?: string;
  delivery_time?: string;
  tracking_no?: string;
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  variant_id: string;
  product_name: string;
  variant_name: string;
  image: string;
  price: number;
  quantity: number;
  total_amount: number;
}

// 支付相关类型
export interface PaymentInfo {
  prepay_id: string;
  timestamp: string;
  nonce_str: string;
  package: string;
  sign_type: string;
  pay_sign: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 搜索和筛选类型
export interface SearchParams {
  q?: string;
  category_id?: string;
  min_price?: number;
  max_price?: number;
  sort?: 'default' | 'price_asc' | 'price_desc' | 'sales_desc' | 'created_desc';
  page?: number;
  size?: number;
}

// 营业执照上传类型
export interface LicenseUpload {
  company_name: string;
  license_no: string;
  contact_name: string;
  contact_phone: string;
  license_image: string;
}

// 轮播图类型
export interface Banner {
  id: string;
  title: string;
  image: string;
  link_type: 'product' | 'category' | 'url';
  link_value: string;
  sort_order: number;
  status: 'active' | 'inactive';
}

// 优惠券类型
export interface Coupon {
  id: string;
  name: string;
  type: 'fixed' | 'percent';
  value: number;
  min_amount: number;
  max_discount?: number;
  start_time: string;
  end_time: string;
  status: 'active' | 'inactive' | 'used' | 'expired';
}

// 全局状态类型
export interface GlobalState {
  user: User | null;
  cart: CartItem[];
  loading: boolean;
  token: string | null;
  refreshToken: string | null;
}

// 组件Props类型
export interface ComponentProps {
  className?: string;
  style?: string;
}

// 事件类型
export interface CustomEvent<T = any> {
  detail: T;
}

// 页面选项类型
export interface PageOptions {
  [key: string]: any;
}

// 上传文件类型
export interface UploadFile {
  url: string;
  size: number;
  type: string;
  name: string;
}

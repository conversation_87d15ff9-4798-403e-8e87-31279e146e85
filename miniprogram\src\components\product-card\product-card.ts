import { Product, User } from '../../types';
import { formatPrice } from '../../utils';
import { useStore } from '../../store';

Component({
  properties: {
    product: {
      type: Object as any,
      value: {} as Product,
    },
    layout: {
      type: String,
      value: 'list', // 'list' | 'grid'
    },
    showAddToCart: {
      type: Boolean,
      value: true,
    },
  },

  data: {
    currentPrice: '',
    originalPrice: '',
    showOriginalPrice: false,
    imageLoaded: false,
  },

  lifetimes: {
    attached() {
      this.updatePriceDisplay();
    },
  },

  observers: {
    'product': function(product: Product) {
      if (product && product.id) {
        this.updatePriceDisplay();
      }
    },
  },

  methods: {
    // 更新价格显示
    updatePriceDisplay() {
      const product = this.data.product as Product;
      if (!product || !product.id) return;

      const store = useStore();
      const user = store.getUser();
      
      // 根据用户角色显示对应价格
      const currentPrice = user?.role === 'B' ? product.price_b : product.price_c;
      const originalPrice = product.original_price;
      
      this.setData({
        currentPrice: formatPrice(currentPrice),
        originalPrice: originalPrice ? formatPrice(originalPrice) : '',
        showOriginalPrice: !!(originalPrice && originalPrice > currentPrice),
      });
    },

    // 点击商品卡片
    onTap() {
      const product = this.data.product as Product;
      if (!product || !product.id) return;

      // 触发自定义事件
      this.triggerEvent('tap', { product });

      // 默认跳转到商品详情页
      wx.navigateTo({
        url: `/pages/product/product?id=${product.id}`,
        fail: (error) => {
          console.error('Navigate to product detail failed:', error);
        },
      });
    },

    // 添加到购物车
    onAddToCart(event: any) {
      event.stopPropagation(); // 阻止事件冒泡

      const product = this.data.product as Product;
      if (!product || !product.id || product.stock <= 0) {
        wx.showToast({
          title: '商品已售罄',
          icon: 'none',
        });
        return;
      }

      // 如果商品有多个规格，跳转到详情页选择规格
      if (product.variants && product.variants.length > 1) {
        wx.navigateTo({
          url: `/pages/product/product?id=${product.id}&action=addToCart`,
        });
        return;
      }

      // 单规格商品直接添加到购物车
      const store = useStore();
      const user = store.getUser();
      
      if (!user) {
        wx.showToast({
          title: '请先登录',
          icon: 'none',
        });
        
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/login/login',
          });
        }, 1500);
        return;
      }

      const variant = product.variants[0];
      const price = user.role === 'B' ? variant.price_b : variant.price_c;

      const cartItem = {
        id: '', // 服务器生成
        product_id: product.id,
        variant_id: variant.id,
        product_name: product.name,
        variant_name: variant.name,
        image: product.images[0] || '',
        price: price,
        quantity: 1,
        stock: variant.stock,
        selected: true,
      };

      store.addToCart(cartItem);

      // 触发添加到购物车事件
      this.triggerEvent('addToCart', { product, variant, cartItem });

      wx.showToast({
        title: '已添加到购物车',
        icon: 'success',
      });

      // 如果用户已登录，同步到服务器
      if (user) {
        this.syncToServer(variant.id, 1);
      }
    },

    // 同步到服务器
    async syncToServer(variantId: string, quantity: number) {
      try {
        const { cartApi } = await import('../../services/api');
        await cartApi.addToCart(variantId, quantity);
      } catch (error) {
        console.error('Failed to sync cart to server:', error);
        // 同步失败不影响本地操作
      }
    },

    // 图片加载成功
    onImageLoad() {
      this.setData({
        imageLoaded: true,
      });
    },

    // 图片加载失败
    onImageError() {
      console.error('Product image load failed:', this.data.product);
      // 可以设置默认图片
    },
  },
});

<view class="home-page">
  <!-- 搜索栏 -->
  <view class="search-bar" bindtap="onSearchTap">
    <view class="search-input">
      <text class="search-icon">🔍</text>
      <text class="search-placeholder">搜索商品</text>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section" wx:if="{{banners.length > 0}}">
    <image-carousel
      images="{{bannerImages}}"
      height="400rpx"
      autoplay="{{true}}"
      interval="{{3000}}"
      show-dots="{{true}}"
      bind:imageTap="onBannerTap"
    />
  </view>

  <!-- 分类入口 -->
  <view class="category-section" wx:if="{{categories.length > 0}}">
    <view class="section-title">商品分类</view>
    <view class="category-grid">
      <view 
        wx:for="{{categories}}" 
        wx:key="id"
        class="category-item"
        bindtap="onCategoryTap"
        data-category="{{item}}"
      >
        <view class="category-icon">
          <image wx:if="{{item.icon}}" src="{{item.icon}}" class="icon-image" />
          <text wx:else class="icon-text">{{item.name.charAt(0)}}</text>
        </view>
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 今日捡漏 -->
  <view class="hot-section" wx:if="{{hotProducts.length > 0}}">
    <view class="section-header">
      <view class="section-title">今日捡漏</view>
      <view class="section-more" bindtap="onViewMoreHot">
        更多 <text class="arrow">›</text>
      </view>
    </view>
    <scroll-view class="hot-products" scroll-x="{{true}}" show-scrollbar="{{false}}">
      <view class="hot-product-list">
        <view 
          wx:for="{{hotProducts}}" 
          wx:key="id"
          class="hot-product-item"
          bindtap="onProductTap"
          data-product="{{item}}"
        >
          <image class="hot-product-image" src="{{item.images[0]}}" mode="aspectFill" />
          <view class="hot-product-info">
            <view class="hot-product-name">{{item.name}}</view>
            <view class="hot-product-price">{{item.displayPrice}}</view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 限时抢购 -->
  <view class="flash-sale-section" wx:if="{{flashSaleProducts.length > 0}}">
    <view class="section-header">
      <view class="section-title">
        <text class="flash-icon">⚡</text>
        限时抢购
      </view>
      <view class="countdown" wx:if="{{countdown > 0}}">
        {{countdownText}}
      </view>
    </view>
    <scroll-view class="flash-sale-products" scroll-x="{{true}}" show-scrollbar="{{false}}">
      <view class="flash-sale-list">
        <view 
          wx:for="{{flashSaleProducts}}" 
          wx:key="id"
          class="flash-sale-item"
          bindtap="onProductTap"
          data-product="{{item}}"
        >
          <image class="flash-sale-image" src="{{item.images[0]}}" mode="aspectFill" />
          <view class="flash-sale-info">
            <view class="flash-sale-name">{{item.name}}</view>
            <view class="flash-sale-prices">
              <text class="sale-price">{{item.displayPrice}}</text>
              <text class="original-price">{{item.originalPrice}}</text>
            </view>
            <view class="flash-sale-progress">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{item.saleProgress}}%"></view>
              </view>
              <text class="progress-text">已抢{{item.saleProgress}}%</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 推荐商品 -->
  <view class="recommend-section" wx:if="{{recommendProducts.length > 0}}">
    <view class="section-title">为你推荐</view>
    <view class="recommend-products">
      <product-card
        wx:for="{{recommendProducts}}"
        wx:key="id"
        product="{{item}}"
        layout="grid"
        bind:tap="onProductTap"
        bind:addToCart="onAddToCart"
      />
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <view wx:if="{{loading}}" class="loading">
      <text class="loading-icon">⏳</text>
      <text class="loading-text">加载中...</text>
    </view>
    <view wx:else class="load-more-btn" bindtap="onLoadMore">
      点击加载更多
    </view>
  </view>

  <!-- 底部占位 -->
  <view class="bottom-placeholder"></view>
</view>

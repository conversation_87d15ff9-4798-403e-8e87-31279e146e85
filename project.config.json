{"miniprogramRoot": "miniprogram/", "cloudfunctionRoot": "cloudfunctions/", "setting": {"urlCheck": true, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "compileWorklet": false, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "appid": "wxff8090a898981fe5", "projectname": "quickstart-wx-cloud", "libVersion": "2.20.1", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "condition": {"search": {"list": []}, "conversation": {"list": []}, "plugin": {"list": []}, "game": {"list": []}, "miniprogram": {"list": [{"id": -1, "name": "db guide", "pathName": "pages/databaseGuide/databaseGuide"}]}}, "compileType": "miniprogram", "srcMiniprogramRoot": "miniprogram/", "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "simulatorPluginLibVersion": {}}
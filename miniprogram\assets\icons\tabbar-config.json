{"description": "TabBar图标配置示例 - 生成图标后将此配置复制到app.json的tabBar部分", "tabBar": {"color": "#666666", "selectedColor": "#2FA85A", "backgroundColor": "#FFFFFF", "borderStyle": "black", "list": [{"pagePath": "pages/home/<USER>", "text": "首页", "iconPath": "assets/icons/home.png", "selectedIconPath": "assets/icons/home-active.png"}, {"pagePath": "pages/category/category", "text": "分类", "iconPath": "assets/icons/category.png", "selectedIconPath": "assets/icons/category-active.png"}, {"pagePath": "pages/cart/cart", "text": "购物车", "iconPath": "assets/icons/cart.png", "selectedIconPath": "assets/icons/cart-active.png"}, {"pagePath": "pages/profile/profile", "text": "我的", "iconPath": "assets/icons/profile.png", "selectedIconPath": "assets/icons/profile-active.png"}]}, "instructions": ["1. 使用 icon-generator.html 生成所需的PNG图标文件", "2. 确保所有图标文件都在 assets/icons/ 目录中", "3. 将上面的tabBar配置复制到 app.json 文件中", "4. 在微信开发者工具中重新编译项目", "5. 检查TabBar是否正常显示图标"], "required_files": ["assets/icons/home.png", "assets/icons/home-active.png", "assets/icons/category.png", "assets/icons/category-active.png", "assets/icons/cart.png", "assets/icons/cart-active.png", "assets/icons/profile.png", "assets/icons/profile-active.png"]}
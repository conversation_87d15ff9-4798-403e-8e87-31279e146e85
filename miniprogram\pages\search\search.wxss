.search-page {
  min-height: 100vh;
  background-color: #F7F9FA;
}

/* 搜索栏 */
.search-header {
  background-color: #FFFFFF;
  padding: 16rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  border-bottom: 1rpx solid #E5E5E5;
}

.search-input-container {
  flex: 1;
  position: relative;
  background-color: #F5F5F5;
  border-radius: 36rpx;
  padding: 0 32rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  height: 100%;
}

.search-input::placeholder {
  color: #999999;
}

.clear-btn {
  width: 32rpx;
  height: 32rpx;
  background-color: #CCCCCC;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #FFFFFF;
}

.search-btn {
  font-size: 28rpx;
  color: #2FA85A;
  padding: 16rpx 0;
}

/* 搜索建议 */
.suggestions {
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #E5E5E5;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-icon {
  font-size: 28rpx;
  color: #999999;
  margin-right: 16rpx;
}

.suggestion-text {
  font-size: 28rpx;
  color: #333333;
}

/* 搜索历史 */
.search-history {
  background-color: #FFFFFF;
  margin: 16rpx 32rpx;
  border-radius: 8rpx;
  padding: 32rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.history-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

.clear-history {
  font-size: 24rpx;
  color: #999999;
}

.history-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.history-tag {
  padding: 12rpx 24rpx;
  background-color: #F5F5F5;
  border-radius: 24rpx;
  font-size: 24rpx;
  color: #666666;
}

/* 热门搜索 */
.hot-search {
  background-color: #FFFFFF;
  margin: 16rpx 32rpx;
  border-radius: 8rpx;
  padding: 32rpx;
}

.hot-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 24rpx;
}

.hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.hot-tag {
  padding: 12rpx 24rpx;
  background-color: #F5F5F5;
  border-radius: 24rpx;
  font-size: 24rpx;
  color: #666666;
  position: relative;
}

.hot-tag.hot-top {
  background-color: #FFF5F0;
  color: #FF6B35;
}

.hot-tag.hot-top::before {
  content: '🔥';
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  font-size: 20rpx;
}

/* 搜索结果 */
.search-results {
  padding-bottom: 32rpx;
}

/* 筛选栏 */
.filter-bar {
  background-color: #FFFFFF;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #E5E5E5;
}

.result-count {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 16rpx;
}

.filter-options {
  display: flex;
  gap: 48rpx;
}

.filter-option {
  font-size: 28rpx;
  color: #666666;
  position: relative;
  padding-bottom: 8rpx;
}

.filter-option.active {
  color: #2FA85A;
  font-weight: 500;
}

.filter-option.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #2FA85A;
  border-radius: 2rpx;
}

/* 商品列表 */
.product-list {
  padding: 0 32rpx;
}

/* 空结果 */
.empty-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 128rpx 32rpx 64rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 36rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 48rpx;
}

/* 推荐商品 */
.recommend-section {
  width: 100%;
}

.recommend-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 24rpx;
  text-align: left;
}

.recommend-list {
  display: flex;
  gap: 24rpx;
  overflow-x: auto;
  padding-bottom: 16rpx;
}

.recommend-item {
  width: 200rpx;
  flex-shrink: 0;
}

.recommend-image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  background-color: #F5F5F5;
  margin-bottom: 12rpx;
}

.recommend-name {
  font-size: 24rpx;
  color: #333333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommend-price {
  font-size: 28rpx;
  color: #2FA85A;
  font-weight: 600;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 64rpx 32rpx;
  gap: 16rpx;
}

.loading-icon {
  font-size: 32rpx;
  animation: rotate 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

.load-more {
  text-align: center;
  padding: 32rpx;
  font-size: 28rpx;
  color: #2FA85A;
  background-color: #FFFFFF;
  margin: 16rpx 32rpx;
  border-radius: 8rpx;
}

.no-more {
  text-align: center;
  padding: 32rpx;
  font-size: 28rpx;
  color: #999999;
}

/* 动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 点击效果 */
.suggestion-item:active,
.history-tag:active,
.hot-tag:active,
.filter-option:active,
.load-more:active {
  background-color: #F0F0F0;
}

<view class="search-page">
  <!-- 搜索栏 -->
  <view class="search-header">
    <view class="search-input-container">
      <input 
        class="search-input"
        type="text"
        placeholder="搜索商品"
        value="{{keyword}}"
        bindinput="onKeywordInput"
        bindconfirm="onSearch"
        focus="{{true}}"
      />
      <view wx:if="{{keyword}}" class="clear-btn" bindtap="onClearKeyword">
        ✕
      </view>
    </view>
    <view class="search-btn" bindtap="onSearch">搜索</view>
  </view>

  <!-- 搜索建议 -->
  <view wx:if="{{showSuggestions && suggestions.length > 0}}" class="suggestions">
    <view 
      wx:for="{{suggestions}}" 
      wx:key="index"
      class="suggestion-item"
      bindtap="onSuggestionTap"
      data-keyword="{{item}}"
    >
      <text class="suggestion-icon">🔍</text>
      <text class="suggestion-text">{{item}}</text>
    </view>
  </view>

  <!-- 搜索历史 -->
  <view wx:if="{{!keyword && searchHistory.length > 0}}" class="search-history">
    <view class="history-header">
      <text class="history-title">搜索历史</text>
      <text class="clear-history" bindtap="onClearHistory">清空</text>
    </view>
    <view class="history-tags">
      <view 
        wx:for="{{searchHistory}}" 
        wx:key="index"
        class="history-tag"
        bindtap="onHistoryTap"
        data-keyword="{{item}}"
      >
        {{item}}
      </view>
    </view>
  </view>

  <!-- 热门搜索 -->
  <view wx:if="{{!keyword && hotKeywords.length > 0}}" class="hot-search">
    <view class="hot-title">热门搜索</view>
    <view class="hot-tags">
      <view 
        wx:for="{{hotKeywords}}" 
        wx:key="index"
        class="hot-tag {{index < 3 ? 'hot-top' : ''}}"
        bindtap="onHotKeywordTap"
        data-keyword="{{item}}"
      >
        {{item}}
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view wx:if="{{hasSearched}}" class="search-results">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="result-count">找到{{totalCount}}件商品</view>
      <view class="filter-options">
        <view 
          wx:for="{{sortOptions}}" 
          wx:key="value"
          class="filter-option {{currentSort === item.value ? 'active' : ''}}"
          bindtap="onSortTap"
          data-sort="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>

    <!-- 商品列表 -->
    <view wx:if="{{products.length > 0}}" class="product-list">
      <product-card
        wx:for="{{products}}"
        wx:key="id"
        product="{{item}}"
        layout="list"
        bind:tap="onProductTap"
        bind:addToCart="onAddToCart"
      />
    </view>

    <!-- 空结果 -->
    <view wx:elif="{{!loading}}" class="empty-result">
      <view class="empty-icon">🔍</view>
      <view class="empty-text">没有找到相关商品</view>
      <view class="empty-desc">试试其他关键词吧</view>
      
      <!-- 推荐商品 -->
      <view wx:if="{{recommendProducts.length > 0}}" class="recommend-section">
        <view class="recommend-title">为你推荐</view>
        <view class="recommend-list">
          <view 
            wx:for="{{recommendProducts}}" 
            wx:key="id"
            class="recommend-item"
            bindtap="onRecommendTap"
            data-product="{{item}}"
          >
            <image class="recommend-image" src="{{item.images[0]}}" mode="aspectFill" />
            <view class="recommend-name">{{item.name}}</view>
            <view class="recommend-price">{{item.displayPrice}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading">
      <text class="loading-icon">⏳</text>
      <text class="loading-text">搜索中...</text>
    </view>

    <!-- 加载更多 -->
    <view wx:elif="{{hasMore}}" class="load-more" bindtap="onLoadMore">
      点击加载更多
    </view>

    <!-- 没有更多 -->
    <view wx:elif="{{products.length > 0}}" class="no-more">
      没有更多商品了
    </view>
  </view>
</view>

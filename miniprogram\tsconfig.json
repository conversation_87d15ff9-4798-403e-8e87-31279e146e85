{"compilerOptions": {"target": "ES2018", "module": "CommonJS", "lib": ["ES2018"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/services/*": ["src/services/*"], "@/store/*": ["src/store/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"]}, "typeRoots": ["./typings", "./node_modules/@types"], "types": ["miniprogram-api-typings"]}, "include": ["src/**/*", "typings/**/*"], "exclude": ["node_modules"]}
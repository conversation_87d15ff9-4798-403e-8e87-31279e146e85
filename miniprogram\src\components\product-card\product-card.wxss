.product-card {
  background: #FFFFFF;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 16rpx;
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 320rpx;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  background-color: #F5F5F5;
}

.stock-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.stock-badge.out-of-stock {
  background-color: #FF4757;
}

.stock-badge.low-stock {
  background-color: #FFA726;
}

.product-info {
  padding: 24rpx;
}

.product-name {
  font-size: 32rpx;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.price-container {
  display: flex;
  align-items: baseline;
  margin-bottom: 16rpx;
}

.current-price {
  font-size: 36rpx;
  color: #2FA85A;
  font-weight: 600;
  margin-right: 16rpx;
}

.original-price {
  font-size: 28rpx;
  color: #999999;
  text-decoration: line-through;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sales {
  font-size: 28rpx;
  color: #666666;
}

.add-to-cart {
  width: 56rpx;
  height: 56rpx;
  background-color: #2FA85A;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-to-cart:active {
  transform: scale(0.95);
  background-color: #268A4A;
}

.add-icon {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 1;
}

/* 网格布局样式 */
.product-card.grid-layout {
  width: calc(50% - 8rpx);
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.product-card.grid-layout:nth-child(2n) {
  margin-right: 0;
}

.product-card.grid-layout .product-image-container {
  height: 240rpx;
}

.product-card.grid-layout .product-info {
  padding: 16rpx;
}

.product-card.grid-layout .product-name {
  font-size: 28rpx;
  margin-bottom: 12rpx;
  -webkit-line-clamp: 1;
}

.product-card.grid-layout .current-price {
  font-size: 32rpx;
}

.product-card.grid-layout .original-price {
  font-size: 24rpx;
}

.product-card.grid-layout .sales {
  font-size: 24rpx;
}

.product-card.grid-layout .add-to-cart {
  width: 48rpx;
  height: 48rpx;
}

.product-card.grid-layout .add-icon {
  font-size: 28rpx;
}

<view class="product-card" bindtap="onTap">
  <view class="product-image-container">
    <image 
      class="product-image" 
      src="{{product.images[0]}}" 
      mode="aspectFill"
      lazy-load="{{true}}"
      bindload="onImageLoad"
      binderror="onImageError"
    />
    <view wx:if="{{product.stock <= 0}}" class="stock-badge out-of-stock">
      售罄
    </view>
    <view wx:elif="{{product.stock <= 10}}" class="stock-badge low-stock">
      仅剩{{product.stock}}件
    </view>
  </view>
  
  <view class="product-info">
    <view class="product-name">{{product.name}}</view>
    
    <view class="price-container">
      <view class="current-price">
        {{currentPrice}}
      </view>
      <view wx:if="{{showOriginalPrice}}" class="original-price">
        {{originalPrice}}
      </view>
    </view>
    
    <view class="product-meta">
      <view class="sales">已售{{product.sales || 0}}</view>
      <view wx:if="{{showAddToCart && product.stock > 0}}" class="add-to-cart" bindtap="onAddToCart">
        <text class="add-icon">+</text>
      </view>
    </view>
  </view>
</view>

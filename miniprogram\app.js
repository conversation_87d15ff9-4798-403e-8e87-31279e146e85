// app.ts
import { useStore } from './src/store';

App({
  onLaunch() {
    console.log('App Launch');

    // 初始化应用
    this.initApp();
  },

  onShow() {
    console.log('App Show');
  },

  onHide() {
    console.log('App Hide');
  },

  onError(error: string) {
    console.error('App Error:', error);

    // 可以在这里上报错误到监控系统
    this.reportError(error);
  },

  // 初始化应用
  async initApp() {
    try {
      // 检查更新
      this.checkUpdate();

      // 初始化全局状态
      this.initGlobalState();

      // 检查登录状态
      await this.checkLoginStatus();

    } catch (error) {
      console.error('App init failed:', error);
    }
  },

  // 检查小程序更新
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();

      updateManager.onCheckForUpdate((res) => {
        console.log('Check for update:', res.hasUpdate);
      });

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          },
        });
      });

      updateManager.onUpdateFailed(() => {
        console.error('Update failed');
      });
    }
  },

  // 初始化全局状态
  initGlobalState() {
    const store = useStore();

    // 从本地存储恢复状态已在store中处理
    console.log('Global state initialized');
  },

  // 检查登录状态
  async checkLoginStatus() {
    const store = useStore();
    const token = store.getState().token;

    if (token) {
      try {
        // 验证token是否有效
        const { userApi } = await import('./src/services/api');
        const response = await userApi.getProfile();

        // 更新用户信息
        store.setUser(response.data);

        console.log('User login status verified');
      } catch (error) {
        console.error('Token verification failed:', error);

        // token无效，清除登录状态
        store.logout();
      }
    }
  },

  // 错误上报
  reportError(error: string) {
    // 这里可以集成错误监控服务，如腾讯云监控、Sentry等
    console.log('Report error:', error);
  },

  globalData: {
    userInfo: null,
    systemInfo: null,
  },
});

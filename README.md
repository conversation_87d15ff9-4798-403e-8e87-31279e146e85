# 刀刀乐捡漏网 - 微信小程序

这是刀刀乐捡漏网的微信小程序前端项目，使用 TypeScript 开发，实现了完整的电商购物功能。

## 项目特性

- 🛍️ 完整的电商购物流程：浏览、搜索、购物车、下单、支付
- 👥 支持 C 端和 B 端用户，差异化价格展示
- 📱 响应式设计，适配各种屏幕尺寸
- 🔐 手机号 + 验证码登录，支持营业执照上传升级 B 端
- 🛒 购物车本地存储，登录后自动同步
- 💳 集成微信支付，支持订单管理
- 🎨 组件化架构，易于维护和扩展
- 📦 TypeScript 开发，类型安全

## 技术栈

- **框架**: 微信原生小程序
- **语言**: TypeScript
- **状态管理**: 自建 Store (Zustand-like)
- **网络请求**: 封装的 HTTP 客户端
- **UI 组件**: 自研组件库
- **构建工具**: 微信开发者工具 + TypeScript 编译器

## 项目结构

```
miniprogram/
├── src/                    # 源代码目录
│   ├── components/         # 公共组件
│   │   ├── product-card/   # 商品卡片组件
│   │   ├── image-carousel/ # 图片轮播组件
│   │   └── ...
│   ├── services/           # API 服务
│   │   ├── http.ts         # HTTP 客户端
│   │   └── api.ts          # API 接口定义
│   ├── store/              # 状态管理
│   │   └── index.ts        # 全局状态
│   ├── types/              # TypeScript 类型定义
│   │   └── index.ts
│   └── utils/              # 工具函数
│       └── index.ts
├── pages/                  # 页面目录
│   ├── home/               # 首页
│   ├── login/              # 登录页
│   ├── product/            # 商品详情
│   ├── cart/               # 购物车
│   ├── checkout/           # 下单页
│   ├── profile/            # 个人中心
│   └── ...
├── assets/                 # 静态资源
│   └── icons/              # 图标资源
├── mock/                   # Mock 数据
│   └── db.json             # 模拟数据库
├── app.js                  # 应用入口
├── app.json                # 应用配置
├── app.wxss                # 全局样式
├── tsconfig.json           # TypeScript 配置
└── package.json            # 项目依赖
```

## 快速开始

### 1. 环境准备

- 安装 [微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- 安装 Node.js (推荐 v16+)

### 2. 安装依赖

```bash
cd miniprogram
npm install
```

### 3. 启动 Mock 服务器

```bash
npm run mock
```

Mock 服务器将在 http://localhost:3000 启动，提供模拟的 API 数据。

### 4. 编译 TypeScript

```bash
npm run build
# 或者监听模式
npm run dev
```

### 5. 打开微信开发者工具

1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目根目录
4. 填写 AppID（测试可使用测试号）
5. 点击"导入"

### 6. 生成TabBar图标

项目需要TabBar图标才能正常运行。有以下几种方式生成图标：

**方式1：使用图标生成器（推荐）**
```bash
# 在浏览器中打开图标生成器
open miniprogram/assets/icons/icon-generator.html
# 点击"一键生成所有图标"按钮下载PNG文件
# 将下载的文件放到 miniprogram/assets/icons/ 目录
```

**方式2：使用开源图标库**
- 访问 [Tabler Icons](https://tabler-icons.io/) 下载图标
- 搜索：home, category, shopping-cart, user
- 下载64x64像素的PNG格式

**方式3：临时跳过图标（快速启动）**
当前配置已移除图标路径，可以直接启动项目。后续添加图标时，在app.json的tabBar中添加iconPath和selectedIconPath。

### 7. 配置开发环境

在 `src/services/http.ts` 中修改 API 基础地址：

```typescript
const ENV_CONFIG = {
  development: {
    baseURL: 'http://localhost:3000/api/v1', // Mock 服务器地址
  },
  // ...
};
```

## 核心功能

### 用户系统
- 手机号 + 验证码登录
- 用户信息管理
- 营业执照上传（B 端升级）
- 登录状态持久化

### 商品系统
- 商品列表展示
- 商品详情查看
- 商品搜索和筛选
- 分类浏览
- 规格选择

### 购物车
- 添加/删除商品
- 数量修改
- 本地存储
- 登录后同步

### 订单系统
- 创建订单
- 支付集成
- 订单状态跟踪
- 订单历史

### 地址管理
- 收货地址增删改查
- 默认地址设置

## API 接口

项目使用 RESTful API 设计，主要接口包括：

- `POST /auth/login` - 用户登录
- `GET /products` - 获取商品列表
- `GET /products/:id` - 获取商品详情
- `POST /cart` - 添加到购物车
- `POST /checkout` - 创建订单
- `POST /users/license` - 上传营业执照

详细的 API 文档请参考 `src/services/api.ts` 文件。

## 组件库

### ProductCard - 商品卡片
```xml
<product-card
  product="{{product}}"
  layout="grid"
  bind:tap="onProductTap"
  bind:addToCart="onAddToCart"
/>
```

### ImageCarousel - 图片轮播
```xml
<image-carousel
  images="{{images}}"
  autoplay="{{true}}"
  bind:imageTap="onImageTap"
/>
```

## 状态管理

使用自建的状态管理系统，类似 Zustand：

```typescript
import { useStore } from './src/store';

const store = useStore();

// 获取用户信息
const user = store.getUser();

// 添加到购物车
store.addToCart(cartItem);

// 监听状态变化
store.subscribe((state) => {
  console.log('State changed:', state);
});
```

## 部署

### 开发环境
1. 启动 Mock 服务器
2. 编译 TypeScript
3. 在微信开发者工具中预览

### 生产环境
1. 修改 API 基础地址为生产环境
2. 编译 TypeScript
3. 在微信开发者工具中上传代码
4. 在微信公众平台提交审核

## 注意事项

1. **域名配置**: 生产环境需要在微信公众平台配置服务器域名
2. **支付配置**: 需要配置微信支付商户号
3. **图标资源**: 需要准备 TabBar 和其他功能图标
4. **隐私协议**: 需要配置用户协议和隐私政策页面

## 开发规范

- 使用 TypeScript 严格模式
- 遵循 ESLint 代码规范
- 组件命名使用 kebab-case
- 文件命名使用 kebab-case
- 接口返回统一使用 `{ code, message, data }` 格式

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 许可证

MIT License


<view class="cart-page">
  <!-- 空购物车状态 -->
  <view wx:if="{{cartItems.length === 0}}" class="empty-cart">
    <view class="empty-icon">🛒</view>
    <view class="empty-text">购物车空空如也</view>
    <view class="empty-desc">快去挑选心仪的商品吧</view>
    <button class="go-shopping-btn" bindtap="onGoShopping">去逛逛</button>
  </view>

  <!-- 购物车列表 -->
  <view wx:else class="cart-content">
    <!-- 全选栏 -->
    <view class="select-all-bar">
      <label class="select-all-item">
        <checkbox 
          value="all" 
          checked="{{allSelected}}" 
          bindtap="onSelectAll"
        />
        <text class="select-all-text">全选</text>
      </label>
      <view class="cart-count">共{{cartItems.length}}件商品</view>
    </view>

    <!-- 商品列表 -->
    <view class="cart-list">
      <view 
        wx:for="{{cartItems}}" 
        wx:key="id"
        class="cart-item {{item.selected ? 'selected' : ''}}"
      >
        <!-- 选择框 -->
        <view class="item-checkbox">
          <checkbox 
            value="{{item.variant_id}}" 
            checked="{{item.selected}}"
            bindtap="onSelectItem"
            data-variant-id="{{item.variant_id}}"
          />
        </view>

        <!-- 商品信息 -->
        <view class="item-content" bindtap="onItemTap" data-item="{{item}}">
          <image class="item-image" src="{{item.image}}" mode="aspectFill" />
          
          <view class="item-info">
            <view class="item-name">{{item.product_name}}</view>
            <view class="item-variant">{{item.variant_name}}</view>
            <view class="item-price">¥{{item.displayPrice}}</view>
            
            <!-- 库存不足提示 -->
            <view wx:if="{{item.quantity > item.stock}}" class="stock-warning">
              库存不足，仅剩{{item.stock}}件
            </view>
          </view>
        </view>

        <!-- 数量控制 -->
        <view class="quantity-control">
          <view 
            class="quantity-btn decrease {{item.quantity <= 1 ? 'disabled' : ''}}"
            bindtap="onDecreaseQuantity"
            data-variant-id="{{item.variant_id}}"
          >
            -
          </view>
          <input 
            class="quantity-input"
            type="number"
            value="{{item.quantity}}"
            bindinput="onQuantityInput"
            data-variant-id="{{item.variant_id}}"
          />
          <view 
            class="quantity-btn increase {{item.quantity >= item.stock ? 'disabled' : ''}}"
            bindtap="onIncreaseQuantity"
            data-variant-id="{{item.variant_id}}"
          >
            +
          </view>
        </view>

        <!-- 删除按钮 -->
        <view 
          class="delete-btn"
          bindtap="onDeleteItem"
          data-variant-id="{{item.variant_id}}"
        >
          🗑️
        </view>
      </view>
    </view>

    <!-- 推荐商品 -->
    <view wx:if="{{recommendProducts.length > 0}}" class="recommend-section">
      <view class="section-title">为你推荐</view>
      <scroll-view class="recommend-list" scroll-x="{{true}}" show-scrollbar="{{false}}">
        <view class="recommend-products">
          <view 
            wx:for="{{recommendProducts}}" 
            wx:key="id"
            class="recommend-item"
            bindtap="onRecommendTap"
            data-product="{{item}}"
          >
            <image class="recommend-image" src="{{item.images[0]}}" mode="aspectFill" />
            <view class="recommend-name">{{item.name}}</view>
            <view class="recommend-price">{{item.displayPrice}}</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 底部结算栏 -->
  <view wx:if="{{cartItems.length > 0}}" class="checkout-bar">
    <view class="checkout-info">
      <view class="selected-count">
        已选择 {{selectedCount}} 件商品
      </view>
      <view class="total-price">
        合计：<text class="price-value">¥{{totalPrice}}</text>
      </view>
    </view>
    <button 
      class="checkout-btn {{selectedCount > 0 ? '' : 'disabled'}}"
      bindtap="onCheckout"
      disabled="{{selectedCount === 0}}"
    >
      去结算
    </button>
  </view>
</view>

import { User } from '../../src/types';
import { orderApi, userApi, authApi } from '../../src/services/api';
import { showConfirm, makePhoneCall } from '../../src/utils';
import { useStore } from '../../src/store';

interface ProfileData {
  user: User | null;
  orderStats: {
    pending: number;
    shipped: number;
    delivered: number;
  };
  licenseStatusText: string;
  licenseStatusClass: string;
}

Page<ProfileData, {}>({
  data: {
    user: null,
    orderStats: {
      pending: 0,
      shipped: 0,
      delivered: 0,
    },
    licenseStatusText: '',
    licenseStatusClass: '',
  },

  onLoad() {
    this.loadUserData();
  },

  onShow() {
    // 每次显示时刷新用户数据
    this.loadUserData();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  // 加载用户数据
  loadUserData() {
    const store = useStore();
    const user = store.getUser();
    
    this.setData({
      user,
    });

    if (user) {
      this.updateLicenseStatus(user);
      this.loadOrderStats();
    }
  },

  // 更新营业执照状态显示
  updateLicenseStatus(user: User) {
    let statusText = '';
    let statusClass = '';

    switch (user.license_status) {
      case 'none':
        statusText = '未上传';
        statusClass = '';
        break;
      case 'uploaded':
        statusText = '已上传';
        statusClass = 'uploaded';
        break;
      case 'under_review':
        statusText = '审核中';
        statusClass = 'under_review';
        break;
      case 'approved':
        statusText = '已通过';
        statusClass = 'approved';
        break;
      case 'rejected':
        statusText = '已拒绝';
        statusClass = 'rejected';
        break;
    }

    this.setData({
      licenseStatusText: statusText,
      licenseStatusClass: statusClass,
    });
  },

  // 加载订单统计
  async loadOrderStats() {
    try {
      // 这里可以调用专门的统计接口，或者分别获取各状态的订单数量
      const [pendingRes, shippedRes, deliveredRes] = await Promise.all([
        orderApi.getOrders('pending', 1, 1),
        orderApi.getOrders('shipped', 1, 1),
        orderApi.getOrders('delivered', 1, 1),
      ]);

      this.setData({
        orderStats: {
          pending: pendingRes.data.total,
          shipped: shippedRes.data.total,
          delivered: deliveredRes.data.total,
        },
      });
    } catch (error) {
      console.error('Failed to load order stats:', error);
    }
  },

  // 刷新数据
  async refreshData() {
    this.loadUserData();
    wx.stopPullDownRefresh();
  },

  // 登录
  onLogin() {
    wx.navigateTo({
      url: '/pages/login/login',
    });
  },

  // 点击订单
  onOrderTap(event: any) {
    const status = event.currentTarget.dataset.status;
    const url = status ? `/pages/order/order?status=${status}` : '/pages/order/order';
    
    wx.navigateTo({
      url,
    });
  },

  // 收货地址
  onAddressTap() {
    wx.navigateTo({
      url: '/pages/address/address',
    });
  },

  // 营业执照上传
  onLicenseTap() {
    wx.navigateTo({
      url: '/pages/license/license',
    });
  },

  // API Token 管理
  onApiTokenTap() {
    wx.navigateTo({
      url: '/pages/api-token/api-token',
    });
  },

  // 意见反馈
  onFeedbackTap() {
    wx.navigateTo({
      url: '/pages/feedback/feedback',
    });
  },

  // 关于我们
  onAboutTap() {
    wx.navigateTo({
      url: '/pages/about/about',
    });
  },

  // 设置
  onSettingsTap() {
    wx.navigateTo({
      url: '/pages/settings/settings',
    });
  },

  // 退出登录
  async onLogout() {
    const confirmed = await showConfirm(
      '确认退出',
      '确定要退出登录吗？'
    );

    if (confirmed) {
      try {
        // 调用退出登录接口
        await authApi.logout();
      } catch (error) {
        console.error('Logout API failed:', error);
        // 即使接口失败也要清除本地状态
      }

      // 清除本地登录状态
      const store = useStore();
      store.logout();

      wx.showToast({
        title: '已退出登录',
        icon: 'success',
      });

      // 刷新页面数据
      this.loadUserData();
    }
  },

  // 拨打客服电话
  onCallService() {
    makePhoneCall('************');
  },

  // 微信客服
  onWechatService() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none',
    });
  },
});

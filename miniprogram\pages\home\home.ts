import { Product, Banner, Category, User } from '../../src/types';
import { homeApi, productApi } from '../../src/services/api';
import { formatPrice } from '../../src/utils';
import { useStore } from '../../src/store';

interface HomeData {
  banners: Banner[];
  bannerImages: string[];
  categories: Category[];
  hotProducts: (Product & { displayPrice: string })[];
  flashSaleProducts: (Product & { 
    displayPrice: string; 
    originalPrice: string; 
    saleProgress: number; 
  })[];
  recommendProducts: (Product & { displayPrice: string })[];
  loading: boolean;
  hasMore: boolean;
  page: number;
  countdown: number;
  countdownText: string;
}

Page<HomeData, {}>({
  data: {
    banners: [],
    bannerImages: [],
    categories: [],
    hotProducts: [],
    flashSaleProducts: [],
    recommendProducts: [],
    loading: false,
    hasMore: true,
    page: 1,
    countdown: 0,
    countdownText: '',
  },

  onLoad() {
    this.loadHomeData();
    this.startCountdown();
  },

  onShow() {
    // 每次显示页面时刷新购物车数量
    this.updateTabBarBadge();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  onReachBottom() {
    this.loadMoreRecommendProducts();
  },

  // 加载首页数据
  async loadHomeData() {
    try {
      wx.showLoading({ title: '加载中...' });
      
      const response = await homeApi.getHomeData();
      const { banners, categories, hot_products, recommend_products } = response.data;
      
      const store = useStore();
      const user = store.getUser();
      
      this.setData({
        banners,
        bannerImages: banners.map(banner => banner.image),
        categories: categories.slice(0, 8), // 只显示前8个分类
        hotProducts: this.formatProductsPrice(hot_products, user),
        flashSaleProducts: this.formatFlashSaleProducts(hot_products.slice(0, 5), user),
        recommendProducts: this.formatProductsPrice(recommend_products, user),
      });
      
    } catch (error) {
      console.error('Failed to load home data:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
      });
    } finally {
      wx.hideLoading();
      wx.stopPullDownRefresh();
    }
  },

  // 刷新数据
  async refreshData() {
    this.setData({
      page: 1,
      hasMore: true,
    });
    await this.loadHomeData();
  },

  // 格式化商品价格
  formatProductsPrice(products: Product[], user: User | null) {
    return products.map(product => ({
      ...product,
      displayPrice: formatPrice(user?.role === 'B' ? product.price_b : product.price_c),
    }));
  },

  // 格式化限时抢购商品
  formatFlashSaleProducts(products: Product[], user: User | null) {
    return products.map(product => {
      const currentPrice = user?.role === 'B' ? product.price_b : product.price_c;
      const originalPrice = product.original_price || currentPrice * 1.2;
      const saleProgress = Math.floor(Math.random() * 80) + 10; // 模拟销售进度
      
      return {
        ...product,
        displayPrice: formatPrice(currentPrice),
        originalPrice: formatPrice(originalPrice),
        saleProgress,
      };
    });
  },

  // 开始倒计时
  startCountdown() {
    // 计算到下一个整点的时间
    const now = new Date();
    const nextHour = new Date(now);
    nextHour.setHours(now.getHours() + 1, 0, 0, 0);
    
    const updateCountdown = () => {
      const now = new Date();
      const diff = nextHour.getTime() - now.getTime();
      
      if (diff <= 0) {
        // 重新计算下一个整点
        nextHour.setHours(nextHour.getHours() + 1);
        return;
      }
      
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);
      
      this.setData({
        countdown: diff,
        countdownText: `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`,
      });
    };
    
    updateCountdown();
    setInterval(updateCountdown, 1000);
  },

  // 更新tabBar徽章
  updateTabBarBadge() {
    const store = useStore();
    const cartCount = store.getCartTotalQuantity();
    
    if (cartCount > 0) {
      wx.setTabBarBadge({
        index: 2, // 购物车tab的索引
        text: cartCount > 99 ? '99+' : cartCount.toString(),
      });
    } else {
      wx.removeTabBarBadge({
        index: 2,
      });
    }
  },

  // 点击搜索
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search',
    });
  },

  // 点击轮播图
  onBannerTap(event: any) {
    const { index } = event.detail;
    const banner = this.data.banners[index];
    
    if (!banner) return;
    
    switch (banner.link_type) {
      case 'product':
        wx.navigateTo({
          url: `/pages/product/product?id=${banner.link_value}`,
        });
        break;
      case 'category':
        wx.navigateTo({
          url: `/pages/category/category?id=${banner.link_value}`,
        });
        break;
      case 'url':
        // 可以使用web-view打开外部链接
        break;
    }
  },

  // 点击分类
  onCategoryTap(event: any) {
    const category = event.currentTarget.dataset.category;
    wx.navigateTo({
      url: `/pages/category/category?id=${category.id}&name=${category.name}`,
    });
  },

  // 点击商品
  onProductTap(event: any) {
    const product = event.detail?.product || event.currentTarget.dataset.product;
    wx.navigateTo({
      url: `/pages/product/product?id=${product.id}`,
    });
  },

  // 添加到购物车
  onAddToCart(event: any) {
    const { product } = event.detail;
    console.log('Added to cart:', product);
    
    // 更新购物车徽章
    this.updateTabBarBadge();
  },

  // 查看更多热门商品
  onViewMoreHot() {
    wx.navigateTo({
      url: '/pages/search/search?type=hot',
    });
  },

  // 加载更多推荐商品
  async loadMoreRecommendProducts() {
    if (this.data.loading || !this.data.hasMore) {
      return;
    }

    this.setData({ loading: true });

    try {
      const response = await productApi.getRecommendProducts(10);
      const store = useStore();
      const user = store.getUser();
      const newProducts = this.formatProductsPrice(response.data, user);
      
      this.setData({
        recommendProducts: [...this.data.recommendProducts, ...newProducts],
        page: this.data.page + 1,
        hasMore: newProducts.length >= 10,
      });
      
    } catch (error) {
      console.error('Failed to load more products:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none',
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 点击加载更多
  onLoadMore() {
    this.loadMoreRecommendProducts();
  },
});

@echo off
echo ========================================
echo   刀刀乐捡漏网小程序开发环境启动器
echo ========================================
echo.

echo 1. 安装依赖...
cd miniprogram
call npm install

echo.
echo 2. 启动 Mock 服务器...
start "Mock Server" cmd /k "npm run mock"

echo.
echo 3. 编译 TypeScript...
start "TypeScript Compiler" cmd /k "npm run dev"

echo.
echo ========================================
echo   开发环境启动完成！
echo ========================================
echo.
echo Mock 服务器: http://localhost:3000
echo 请在微信开发者工具中打开项目根目录
echo.
echo 📋 重要提示：
echo 1. 如需TabBar图标，请打开以下文件生成：
echo    assets/icons/icon-generator.html
echo 2. 生成图标后，更新app.json中的tabBar配置
echo 3. 项目文档：README.md
echo.
pause

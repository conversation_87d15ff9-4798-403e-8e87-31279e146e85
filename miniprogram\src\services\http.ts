import { ApiResponse } from '../types';

// 环境配置
const ENV_CONFIG = {
  development: {
    baseURL: 'http://localhost:3000/api/v1',
  },
  staging: {
    baseURL: 'https://staging-api.daodaole.com/api/v1',
  },
  production: {
    baseURL: 'https://api.daodaole.com/api/v1',
  },
};

// 当前环境
const currentEnv = 'development'; // 可通过构建工具注入

interface RequestOptions {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: any;
  header?: Record<string, string>;
  timeout?: number;
  retry?: number;
  showLoading?: boolean;
  loadingText?: string;
}

interface RequestInterceptor {
  request?: (options: RequestOptions) => RequestOptions;
  response?: (response: any) => any;
  error?: (error: any) => any;
}

class HttpClient {
  private baseURL: string;
  private defaultTimeout: number = 10000;
  private interceptors: RequestInterceptor = {};

  constructor() {
    this.baseURL = ENV_CONFIG[currentEnv as keyof typeof ENV_CONFIG].baseURL;
    this.setupInterceptors();
  }

  // 设置拦截器
  private setupInterceptors() {
    this.interceptors = {
      request: (options: RequestOptions) => {
        // 自动添加token
        const token = wx.getStorageSync('token');
        if (token) {
          options.header = {
            ...options.header,
            'Authorization': `Bearer ${token}`,
          };
        }

        // 添加通用header
        options.header = {
          'Content-Type': 'application/json',
          ...options.header,
        };

        return options;
      },

      response: (response: any) => {
        const { statusCode, data } = response;

        // HTTP状态码检查
        if (statusCode >= 200 && statusCode < 300) {
          // 业务状态码检查
          if (data.code === 0) {
            return data;
          } else {
            // 业务错误
            this.handleBusinessError(data);
            return Promise.reject(data);
          }
        } else {
          // HTTP错误
          this.handleHttpError(response);
          return Promise.reject(response);
        }
      },

      error: (error: any) => {
        this.handleNetworkError(error);
        return Promise.reject(error);
      },
    };
  }

  // 处理业务错误
  private handleBusinessError(data: ApiResponse) {
    switch (data.code) {
      case 401:
        // token过期，尝试刷新
        this.refreshToken();
        break;
      case 403:
        wx.showToast({
          title: '权限不足',
          icon: 'none',
        });
        break;
      default:
        wx.showToast({
          title: data.message || '请求失败',
          icon: 'none',
        });
    }
  }

  // 处理HTTP错误
  private handleHttpError(response: any) {
    const { statusCode } = response;
    let message = '网络错误';

    switch (statusCode) {
      case 400:
        message = '请求参数错误';
        break;
      case 401:
        message = '未授权，请重新登录';
        this.redirectToLogin();
        break;
      case 403:
        message = '权限不足';
        break;
      case 404:
        message = '请求的资源不存在';
        break;
      case 500:
        message = '服务器内部错误';
        break;
      case 502:
        message = '网关错误';
        break;
      case 503:
        message = '服务不可用';
        break;
      default:
        message = `网络错误 ${statusCode}`;
    }

    wx.showToast({
      title: message,
      icon: 'none',
    });
  }

  // 处理网络错误
  private handleNetworkError(error: any) {
    console.error('Network Error:', error);
    wx.showToast({
      title: '网络连接失败，请检查网络设置',
      icon: 'none',
    });
  }

  // 刷新token
  private async refreshToken() {
    try {
      const refreshToken = wx.getStorageSync('refreshToken');
      if (!refreshToken) {
        this.redirectToLogin();
        return;
      }

      const response = await this.request({
        url: '/auth/refresh',
        method: 'POST',
        data: { refresh_token: refreshToken },
      });

      if (response.data.token) {
        wx.setStorageSync('token', response.data.token);
        wx.setStorageSync('refreshToken', response.data.refresh_token);
      } else {
        this.redirectToLogin();
      }
    } catch (error) {
      this.redirectToLogin();
    }
  }

  // 跳转到登录页
  private redirectToLogin() {
    wx.removeStorageSync('token');
    wx.removeStorageSync('refreshToken');
    wx.removeStorageSync('user');
    
    wx.reLaunch({
      url: '/pages/login/login',
    });
  }

  // 主要请求方法
  async request<T = any>(options: RequestOptions): Promise<ApiResponse<T>> {
    const {
      url,
      method = 'GET',
      data,
      header,
      timeout = this.defaultTimeout,
      retry = 1,
      showLoading = false,
      loadingText = '加载中...',
    } = options;

    // 显示加载提示
    if (showLoading) {
      wx.showLoading({
        title: loadingText,
        mask: true,
      });
    }

    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;

    // 应用请求拦截器
    const processedOptions = this.interceptors.request ? 
      this.interceptors.request({ ...options, url: fullUrl }) : 
      { ...options, url: fullUrl };

    try {
      const response = await new Promise<any>((resolve, reject) => {
        wx.request({
          url: processedOptions.url,
          method: method as any,
          data: data,
          header: processedOptions.header,
          timeout: timeout,
          success: resolve,
          fail: reject,
        });
      });

      // 隐藏加载提示
      if (showLoading) {
        wx.hideLoading();
      }

      // 应用响应拦截器
      return this.interceptors.response ? 
        this.interceptors.response(response) : 
        response;

    } catch (error) {
      // 隐藏加载提示
      if (showLoading) {
        wx.hideLoading();
      }

      // 重试逻辑
      if (retry > 0) {
        console.log(`Request failed, retrying... (${retry} attempts left)`);
        return this.request({ ...options, retry: retry - 1 });
      }

      // 应用错误拦截器
      if (this.interceptors.error) {
        this.interceptors.error(error);
      }

      throw error;
    }
  }

  // 便捷方法
  get<T = any>(url: string, params?: any, options?: Partial<RequestOptions>): Promise<ApiResponse<T>> {
    const queryString = params ? this.buildQueryString(params) : '';
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    
    return this.request<T>({
      url: fullUrl,
      method: 'GET',
      ...options,
    });
  }

  post<T = any>(url: string, data?: any, options?: Partial<RequestOptions>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      ...options,
    });
  }

  put<T = any>(url: string, data?: any, options?: Partial<RequestOptions>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      ...options,
    });
  }

  delete<T = any>(url: string, options?: Partial<RequestOptions>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'DELETE',
      ...options,
    });
  }

  // 构建查询字符串
  private buildQueryString(params: Record<string, any>): string {
    return Object.keys(params)
      .filter(key => params[key] !== undefined && params[key] !== null)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
  }

  // 上传文件
  async uploadFile(filePath: string, name: string = 'file', formData?: Record<string, any>): Promise<ApiResponse> {
    const token = wx.getStorageSync('token');
    
    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: `${this.baseURL}/upload`,
        filePath,
        name,
        formData,
        header: {
          'Authorization': token ? `Bearer ${token}` : '',
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.code === 0) {
              resolve(data);
            } else {
              reject(data);
            }
          } catch (error) {
            reject({ code: -1, message: '响应解析失败', data: null });
          }
        },
        fail: reject,
      });
    });
  }
}

// 导出单例
export const http = new HttpClient();
export default http;

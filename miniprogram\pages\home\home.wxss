.home-page {
  background-color: #F7F9FA;
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  padding: 16rpx 32rpx;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #E5E5E5;
}

.search-input {
  height: 72rpx;
  background-color: #F5F5F5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
}

.search-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  color: #999999;
}

.search-placeholder {
  font-size: 28rpx;
  color: #999999;
}

/* 轮播图 */
.banner-section {
  margin: 16rpx 32rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

/* 分类 */
.category-section {
  background-color: #FFFFFF;
  margin: 16rpx 32rpx;
  border-radius: 8rpx;
  padding: 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

.category-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 32rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(25% - 24rpx);
}

.category-icon {
  width: 96rpx;
  height: 96rpx;
  background-color: #F5F5F5;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.icon-image {
  width: 64rpx;
  height: 64rpx;
}

.icon-text {
  font-size: 32rpx;
  color: #2FA85A;
  font-weight: 600;
}

.category-name {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

/* 通用区块样式 */
.hot-section,
.flash-sale-section,
.recommend-section {
  background-color: #FFFFFF;
  margin: 16rpx 32rpx;
  border-radius: 8rpx;
  padding: 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-more {
  font-size: 28rpx;
  color: #2FA85A;
  display: flex;
  align-items: center;
}

.arrow {
  font-size: 32rpx;
  margin-left: 8rpx;
}

/* 今日捡漏 */
.hot-products {
  white-space: nowrap;
}

.hot-product-list {
  display: inline-flex;
  gap: 24rpx;
}

.hot-product-item {
  width: 240rpx;
  flex-shrink: 0;
}

.hot-product-image {
  width: 100%;
  height: 240rpx;
  border-radius: 8rpx;
  background-color: #F5F5F5;
}

.hot-product-info {
  padding: 16rpx 0;
}

.hot-product-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hot-product-price {
  font-size: 32rpx;
  color: #2FA85A;
  font-weight: 600;
}

/* 限时抢购 */
.flash-icon {
  color: #FF6B35;
  margin-right: 8rpx;
}

.countdown {
  font-size: 24rpx;
  color: #FF6B35;
  background-color: #FFF5F0;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

.flash-sale-products {
  white-space: nowrap;
}

.flash-sale-list {
  display: inline-flex;
  gap: 24rpx;
}

.flash-sale-item {
  width: 280rpx;
  flex-shrink: 0;
}

.flash-sale-image {
  width: 100%;
  height: 280rpx;
  border-radius: 8rpx;
  background-color: #F5F5F5;
}

.flash-sale-info {
  padding: 16rpx 0;
}

.flash-sale-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.flash-sale-prices {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}

.sale-price {
  font-size: 32rpx;
  color: #FF6B35;
  font-weight: 600;
  margin-right: 16rpx;
}

.original-price {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
}

.flash-sale-progress {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background-color: #F5F5F5;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #FF6B35;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 20rpx;
  color: #FF6B35;
}

/* 推荐商品 */
.recommend-products {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

/* 加载更多 */
.load-more {
  padding: 32rpx;
  text-align: center;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  color: #999999;
}

.loading-icon {
  font-size: 32rpx;
  animation: rotate 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
}

.load-more-btn {
  padding: 24rpx 48rpx;
  background-color: #F5F5F5;
  color: #666666;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: inline-block;
}

.load-more-btn:active {
  background-color: #E5E5E5;
}

/* 底部占位 */
.bottom-placeholder {
  height: 32rpx;
}

/* 动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

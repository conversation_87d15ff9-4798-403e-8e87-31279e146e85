<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TabBar图标生成器 - 刀刀乐捡漏网</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2FA85A;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 1.1em;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        .icon-card {
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            transition: all 0.3s ease;
        }
        .icon-card:hover {
            border-color: #2FA85A;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(47, 168, 90, 0.15);
        }
        .icon-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        .icon-preview {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }
        .icon-demo {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            position: relative;
            transition: transform 0.2s ease;
        }
        .icon-demo:hover {
            transform: scale(1.1);
        }
        .icon-normal {
            background: linear-gradient(145deg, #666666, #555555);
        }
        .icon-active {
            background: linear-gradient(145deg, #2FA85A, #268A4A);
        }
        .icon-label {
            font-size: 11px;
            margin-top: 8px;
            color: #666;
            font-weight: 500;
        }
        .download-section {
            margin-top: 15px;
        }
        .download-btn {
            background: linear-gradient(145deg, #2FA85A, #268A4A);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 3px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(47, 168, 90, 0.3);
        }
        .download-all {
            text-align: center;
            margin: 30px 0;
        }
        .download-all .download-btn {
            font-size: 16px;
            padding: 15px 30px;
            background: linear-gradient(145deg, #667eea, #764ba2);
        }
        .instructions {
            background: linear-gradient(145deg, #e8f5e8, #f0f8f0);
            border: 2px solid #2FA85A;
            border-radius: 12px;
            padding: 25px;
            margin-top: 30px;
        }
        .instructions h3 {
            color: #2FA85A;
            margin-top: 0;
            font-size: 1.4em;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.5;
        }
        .highlight {
            background: linear-gradient(145deg, #fff3cd, #ffeaa7);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f39c12;
            margin: 15px 0;
        }
        canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 TabBar图标生成器</h1>
        <p class="subtitle">为刀刀乐捡漏网小程序生成专业的TabBar图标</p>
        
        <div class="icon-grid">
            <div class="icon-card">
                <div class="icon-title">🏠 首页</div>
                <div class="icon-preview">
                    <div>
                        <div class="icon-demo icon-normal">🏠</div>
                        <div class="icon-label">未选中</div>
                    </div>
                    <div>
                        <div class="icon-demo icon-active">🏠</div>
                        <div class="icon-label">选中</div>
                    </div>
                </div>
                <div class="download-section">
                    <button class="download-btn" onclick="generateIcon('home', '🏠')">生成图标</button>
                </div>
            </div>

            <div class="icon-card">
                <div class="icon-title">📂 分类</div>
                <div class="icon-preview">
                    <div>
                        <div class="icon-demo icon-normal">📂</div>
                        <div class="icon-label">未选中</div>
                    </div>
                    <div>
                        <div class="icon-demo icon-active">📂</div>
                        <div class="icon-label">选中</div>
                    </div>
                </div>
                <div class="download-section">
                    <button class="download-btn" onclick="generateIcon('category', '📂')">生成图标</button>
                </div>
            </div>

            <div class="icon-card">
                <div class="icon-title">🛒 购物车</div>
                <div class="icon-preview">
                    <div>
                        <div class="icon-demo icon-normal">🛒</div>
                        <div class="icon-label">未选中</div>
                    </div>
                    <div>
                        <div class="icon-demo icon-active">🛒</div>
                        <div class="icon-label">选中</div>
                    </div>
                </div>
                <div class="download-section">
                    <button class="download-btn" onclick="generateIcon('cart', '🛒')">生成图标</button>
                </div>
            </div>

            <div class="icon-card">
                <div class="icon-title">👤 我的</div>
                <div class="icon-preview">
                    <div>
                        <div class="icon-demo icon-normal">👤</div>
                        <div class="icon-label">未选中</div>
                    </div>
                    <div>
                        <div class="icon-demo icon-active">👤</div>
                        <div class="icon-label">选中</div>
                    </div>
                </div>
                <div class="download-section">
                    <button class="download-btn" onclick="generateIcon('profile', '👤')">生成图标</button>
                </div>
            </div>
        </div>

        <div class="download-all">
            <button class="download-btn" onclick="generateAllIcons()">
                🚀 一键生成所有图标
            </button>
        </div>

        <div class="highlight">
            <strong>💡 提示：</strong> 点击"生成图标"按钮会自动下载对应的PNG图标文件。生成的图标尺寸为64x64像素，适合微信小程序TabBar使用。
        </div>

        <div class="instructions">
            <h3>📋 使用指南</h3>
            <ol>
                <li><strong>生成图标：</strong> 点击各个图标的"生成图标"按钮，或使用"一键生成所有图标"</li>
                <li><strong>保存文件：</strong> 将下载的PNG文件保存到 <code>miniprogram/assets/icons/</code> 目录</li>
                <li><strong>更新配置：</strong> 在 <code>app.json</code> 中添加TabBar图标配置</li>
                <li><strong>重新编译：</strong> 在微信开发者工具中重新编译项目</li>
            </ol>
            
            <h4>🔧 app.json 配置示例</h4>
            <div class="code-block">
"tabBar": {
  "color": "#666666",
  "selectedColor": "#2FA85A",
  "backgroundColor": "#FFFFFF",
  "borderStyle": "black",
  "list": [
    {
      "pagePath": "pages/home/<USER>",
      "text": "首页",
      "iconPath": "assets/icons/home.png",
      "selectedIconPath": "assets/icons/home-active.png"
    },
    {
      "pagePath": "pages/category/category",
      "text": "分类",
      "iconPath": "assets/icons/category.png",
      "selectedIconPath": "assets/icons/category-active.png"
    },
    {
      "pagePath": "pages/cart/cart",
      "text": "购物车",
      "iconPath": "assets/icons/cart.png",
      "selectedIconPath": "assets/icons/cart-active.png"
    },
    {
      "pagePath": "pages/profile/profile",
      "text": "我的",
      "iconPath": "assets/icons/profile.png",
      "selectedIconPath": "assets/icons/profile-active.png"
    }
  ]
}
            </div>
        </div>
    </div>

    <canvas id="canvas" width="64" height="64"></canvas>

    <script>
        function createIconCanvas(emoji, backgroundColor, size = 64) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // 设置画布大小
            canvas.width = size;
            canvas.height = size;
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 绘制圆角背景
            const radius = size * 0.125; // 8px for 64px canvas
            ctx.fillStyle = backgroundColor;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // 绘制emoji图标
            ctx.font = `${size * 0.5}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = 'white';
            ctx.fillText(emoji, size / 2, size / 2);
            
            return canvas.toDataURL('image/png');
        }

        function downloadImage(dataUrl, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = dataUrl;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function generateIcon(name, emoji) {
            // 生成未选中状态图标 (灰色背景)
            const normalIcon = createIconCanvas(emoji, '#666666');
            downloadImage(normalIcon, `${name}.png`);
            
            // 延迟生成选中状态图标 (绿色背景)
            setTimeout(() => {
                const activeIcon = createIconCanvas(emoji, '#2FA85A');
                downloadImage(activeIcon, `${name}-active.png`);
            }, 300);
            
            // 显示成功提示
            showToast(`${name} 图标生成成功！`);
        }

        function generateAllIcons() {
            const icons = [
                { name: 'home', emoji: '🏠' },
                { name: 'category', emoji: '📂' },
                { name: 'cart', emoji: '🛒' },
                { name: 'profile', emoji: '👤' }
            ];

            icons.forEach((icon, index) => {
                setTimeout(() => {
                    generateIcon(icon.name, icon.emoji);
                }, index * 800); // 延迟生成，避免浏览器阻止下载
            });

            showToast('开始生成所有图标，请稍等...', 3000);
        }

        function showToast(message, duration = 2000) {
            // 创建toast元素
            const toast = document.createElement('div');
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #2FA85A;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-weight: 500;
                animation: slideIn 0.3s ease;
            `;
            
            // 添加动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
            
            document.body.appendChild(toast);
            
            // 自动移除
            setTimeout(() => {
                toast.style.animation = 'slideIn 0.3s ease reverse';
                setTimeout(() => {
                    document.body.removeChild(toast);
                    document.head.removeChild(style);
                }, 300);
            }, duration);
        }

        // 添加 roundRect polyfill for older browsers
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }

        // 页面加载完成提示
        window.onload = function() {
            console.log('🎨 TabBar图标生成器已就绪');
            showToast('图标生成器已就绪，点击按钮开始生成图标！', 3000);
        };
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>创建简单图标</title>
</head>
<body>
    <h1>正在生成TabBar图标...</h1>
    <p>请稍等，图标将自动下载到您的下载文件夹。</p>
    
    <canvas id="canvas" width="64" height="64" style="display: none;"></canvas>
    
    <script>
        // 创建简单的图标
        function createSimpleIcon(text, bgColor, textColor = 'white') {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, 64, 64);
            
            // 绘制背景
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, 64, 64);
            
            // 绘制文字
            ctx.fillStyle = textColor;
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, 32, 32);
            
            return canvas.toDataURL('image/png');
        }
        
        // 下载图片
        function downloadImage(dataUrl, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = dataUrl;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        // 生成所有图标
        function generateAllIcons() {
            const icons = [
                { name: 'home', text: '首', normal: '#666666', active: '#2FA85A' },
                { name: 'category', text: '分', normal: '#666666', active: '#2FA85A' },
                { name: 'cart', text: '购', normal: '#666666', active: '#2FA85A' },
                { name: 'profile', text: '我', normal: '#666666', active: '#2FA85A' }
            ];
            
            icons.forEach((icon, index) => {
                setTimeout(() => {
                    // 生成普通状态图标
                    const normalIcon = createSimpleIcon(icon.text, icon.normal);
                    downloadImage(normalIcon, `${icon.name}.png`);
                    
                    // 生成激活状态图标
                    setTimeout(() => {
                        const activeIcon = createSimpleIcon(icon.text, icon.active);
                        downloadImage(activeIcon, `${icon.name}-active.png`);
                    }, 200);
                }, index * 500);
            });
        }
        
        // 页面加载后自动生成图标
        window.onload = function() {
            setTimeout(generateAllIcons, 1000);
        };
    </script>
</body>
</html>

/**app.wxss**/
/* 全局样式 */
page {
  background-color: #F7F9FA;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

/* 通用容器 */
.container {
  padding: 0 32rpx;
}

.page-container {
  min-height: 100vh;
  background-color: #F7F9FA;
}

/* 按钮重置 */
button {
  background: initial;
}

button:focus{
  outline: 0;
}

button::after{
  border: none;
}

/* 文本样式 */
.text-primary {
  color: #333333;
}

.text-secondary {
  color: #666666;
}

.text-muted {
  color: #999999;
}

.text-success {
  color: #2FA85A;
}

.text-danger {
  color: #FF4757;
}

.text-warning {
  color: #FFA726;
}

/* 字体大小 */
.text-xs {
  font-size: 24rpx;
}

.text-sm {
  font-size: 28rpx;
}

.text-base {
  font-size: 32rpx;
}

.text-lg {
  font-size: 36rpx;
}

.text-xl {
  font-size: 40rpx;
}

/* 布局 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 间距 */
.m-0 { margin: 0; }
.m-1 { margin: 8rpx; }
.m-2 { margin: 16rpx; }
.m-3 { margin: 24rpx; }
.m-4 { margin: 32rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8rpx; }
.mt-2 { margin-top: 16rpx; }
.mt-3 { margin-top: 24rpx; }
.mt-4 { margin-top: 32rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8rpx; }
.mb-2 { margin-bottom: 16rpx; }
.mb-3 { margin-bottom: 24rpx; }
.mb-4 { margin-bottom: 32rpx; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 8rpx; }
.ml-2 { margin-left: 16rpx; }
.ml-3 { margin-left: 24rpx; }
.ml-4 { margin-left: 32rpx; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 8rpx; }
.mr-2 { margin-right: 16rpx; }
.mr-3 { margin-right: 24rpx; }
.mr-4 { margin-right: 32rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 8rpx; }
.p-2 { padding: 16rpx; }
.p-3 { padding: 24rpx; }
.p-4 { padding: 32rpx; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 8rpx; }
.pt-2 { padding-top: 16rpx; }
.pt-3 { padding-top: 24rpx; }
.pt-4 { padding-top: 32rpx; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 8rpx; }
.pb-2 { padding-bottom: 16rpx; }
.pb-3 { padding-bottom: 24rpx; }
.pb-4 { padding-bottom: 32rpx; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 8rpx; }
.pl-2 { padding-left: 16rpx; }
.pl-3 { padding-left: 24rpx; }
.pl-4 { padding-left: 32rpx; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 8rpx; }
.pr-2 { padding-right: 16rpx; }
.pr-3 { padding-right: 24rpx; }
.pr-4 { padding-right: 32rpx; }

/* 圆角 */
.rounded {
  border-radius: 8rpx;
}

.rounded-lg {
  border-radius: 16rpx;
}

.rounded-full {
  border-radius: 50%;
}

/* 阴影 */
.shadow {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

/* 背景色 */
.bg-white {
  background-color: #FFFFFF;
}

.bg-gray {
  background-color: #F7F9FA;
}

.bg-primary {
  background-color: #2FA85A;
}

/* 边框 */
.border {
  border: 1rpx solid #E5E5E5;
}

.border-t {
  border-top: 1rpx solid #E5E5E5;
}

.border-b {
  border-bottom: 1rpx solid #E5E5E5;
}

/* 宽高 */
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
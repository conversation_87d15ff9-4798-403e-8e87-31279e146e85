# 快速修复TabBar图标问题

## 问题描述
微信小程序TabBar需要PNG格式的图标文件，但当前缺少这些文件导致模拟器启动失败。

## 解决方案

### 方案1：使用图标生成器（推荐）
1. 在浏览器中打开 `assets/icons/icon-generator.html`
2. 点击"一键生成所有图标"按钮
3. 下载生成的PNG文件到 `assets/icons/` 目录
4. 重新启动微信开发者工具

### 方案2：临时移除图标配置
如果需要快速启动项目进行开发，可以临时移除TabBar的图标配置：

在 `app.json` 中将tabBar配置修改为：
```json
"tabBar": {
  "color": "#666666",
  "selectedColor": "#2FA85A",
  "backgroundColor": "#FFFFFF",
  "borderStyle": "black",
  "list": [
    {
      "pagePath": "pages/home/<USER>",
      "text": "首页"
    },
    {
      "pagePath": "pages/category/category",
      "text": "分类"
    },
    {
      "pagePath": "pages/cart/cart",
      "text": "购物车"
    },
    {
      "pagePath": "pages/profile/profile",
      "text": "我的"
    }
  ]
}
```

### 方案3：使用开源图标库
从以下开源图标库下载合适的图标：

1. **Tabler Icons** (推荐)
   - 网址：https://tabler-icons.io/
   - 搜索：home, category, shopping-cart, user
   - 下载SVG格式，然后转换为PNG

2. **Heroicons**
   - 网址：https://heroicons.com/
   - 选择outline风格的图标

3. **Feather Icons**
   - 网址：https://feathericons.com/
   - 简洁的线条风格图标

4. **Lucide**
   - 网址：https://lucide.dev/
   - Feather Icons的继承者

### 方案4：在线图标转换
使用在线工具将SVG转换为PNG：
1. 访问 https://convertio.co/svg-png/
2. 上传SVG文件
3. 设置尺寸为64x64像素
4. 下载PNG文件

## 图标规范
- **尺寸**: 64x64像素（推荐）或128x128像素
- **格式**: PNG格式，支持透明背景
- **颜色**: 
  - 未选中状态：#666666（灰色）
  - 选中状态：#2FA85A（主题绿色）
- **风格**: 简洁的线条或填充风格

## 文件命名
确保图标文件按以下方式命名：
- `home.png` / `home-active.png`
- `category.png` / `category-active.png`
- `cart.png` / `cart-active.png`
- `profile.png` / `profile-active.png`

## 验证步骤
1. 确认所有PNG文件都在 `assets/icons/` 目录中
2. 检查文件大小（应该在1-10KB之间）
3. 在微信开发者工具中重新编译
4. 查看TabBar是否正常显示图标

## 注意事项
- 微信小程序TabBar不支持SVG格式，必须使用PNG
- 图标文件路径区分大小写
- 建议图标保持一致的视觉风格
- 可以先使用emoji图标快速启动项目，后续再替换为专业图标

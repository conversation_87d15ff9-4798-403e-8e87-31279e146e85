import { Category, Product, User, SearchParams } from '../../src/types';
import { categoryApi, productApi } from '../../src/services/api';
import { formatPrice } from '../../src/utils';
import { useStore } from '../../src/store';

interface CategoryData {
  categories: Category[];
  currentCategory: Category | null;
  currentCategoryId: string;
  products: (Product & { displayPrice: string })[];
  totalCount: number;
  currentSort: string;
  layout: 'grid' | 'list';
  loading: boolean;
  refreshing: boolean;
  hasMore: boolean;
  page: number;
  sortOptions: Array<{ label: string; value: string }>;
}

Page<CategoryData, {}>({
  data: {
    categories: [],
    currentCategory: null,
    currentCategoryId: '',
    products: [],
    totalCount: 0,
    currentSort: 'default',
    layout: 'grid',
    loading: false,
    refreshing: false,
    hasMore: true,
    page: 1,
    sortOptions: [
      { label: '综合', value: 'default' },
      { label: '价格', value: 'price_asc' },
      { label: '销量', value: 'sales_desc' },
      { label: '最新', value: 'created_desc' },
    ],
  },

  onLoad(options: any) {
    // 从URL参数获取分类ID
    const categoryId = options.id;
    const categoryName = options.name;
    
    if (categoryId) {
      this.setData({
        currentCategoryId: categoryId,
      });
      
      // 如果有分类名称，先设置标题
      if (categoryName) {
        wx.setNavigationBarTitle({
          title: categoryName,
        });
      }
    }

    this.loadCategories();
  },

  onShow() {
    // 更新购物车徽章
    this.updateTabBarBadge();
  },

  // 加载分类列表
  async loadCategories() {
    try {
      const response = await categoryApi.getCategories();
      const categories = response.data;
      
      this.setData({
        categories,
      });

      // 如果没有指定分类，默认选择第一个
      if (!this.data.currentCategoryId && categories.length > 0) {
        this.setData({
          currentCategoryId: categories[0].id,
          currentCategory: categories[0],
        });
        
        wx.setNavigationBarTitle({
          title: categories[0].name,
        });
      } else if (this.data.currentCategoryId) {
        // 找到当前分类
        const currentCategory = categories.find(cat => cat.id === this.data.currentCategoryId);
        if (currentCategory) {
          this.setData({
            currentCategory,
          });
          
          wx.setNavigationBarTitle({
            title: currentCategory.name,
          });
        }
      }

      // 加载商品列表
      this.loadProducts();
      
    } catch (error) {
      console.error('Failed to load categories:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
      });
    }
  },

  // 加载商品列表
  async loadProducts(reset: boolean = true) {
    if (this.data.loading) return;

    const { currentCategoryId, currentSort, page } = this.data;
    
    if (!currentCategoryId) return;

    this.setData({ loading: true });

    try {
      const params: SearchParams = {
        category_id: currentCategoryId,
        sort: currentSort as any,
        page: reset ? 1 : page,
        size: 20,
      };

      const response = await categoryApi.getCategoryProducts(currentCategoryId, params);
      const { items, total } = response.data;
      
      const store = useStore();
      const user = store.getUser();
      
      // 格式化商品价格
      const formattedProducts = items.map(product => ({
        ...product,
        displayPrice: formatPrice(user?.role === 'B' ? product.price_b : product.price_c),
      }));

      this.setData({
        products: reset ? formattedProducts : [...this.data.products, ...formattedProducts],
        totalCount: total,
        hasMore: items.length >= 20,
        page: reset ? 2 : page + 1,
      });

    } catch (error) {
      console.error('Failed to load products:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
      });
    } finally {
      this.setData({ 
        loading: false,
        refreshing: false,
      });
    }
  },

  // 点击分类
  onCategoryTap(event: any) {
    const category = event.currentTarget.dataset.category;
    
    if (category.id === this.data.currentCategoryId) {
      return;
    }

    this.setData({
      currentCategoryId: category.id,
      currentCategory: category,
      products: [],
      page: 1,
      hasMore: true,
    });

    wx.setNavigationBarTitle({
      title: category.name,
    });

    this.loadProducts();
  },

  // 点击排序
  onSortTap(event: any) {
    const sort = event.currentTarget.dataset.sort;
    
    if (sort === this.data.currentSort) {
      return;
    }

    this.setData({
      currentSort: sort,
      products: [],
      page: 1,
      hasMore: true,
    });

    this.loadProducts();
  },

  // 切换布局
  onLayoutSwitch() {
    const layout = this.data.layout === 'grid' ? 'list' : 'grid';
    this.setData({ layout });
    
    // 保存用户偏好
    wx.setStorageSync('categoryLayout', layout);
  },

  // 下拉刷新
  onRefresh() {
    this.setData({ refreshing: true });
    this.loadProducts();
  },

  // 加载更多
  onLoadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadProducts(false);
    }
  },

  // 点击商品
  onProductTap(event: any) {
    const { product } = event.detail;
    wx.navigateTo({
      url: `/pages/product/product?id=${product.id}`,
    });
  },

  // 添加到购物车
  onAddToCart(event: any) {
    const { product } = event.detail;
    console.log('Added to cart:', product);
    
    // 更新购物车徽章
    this.updateTabBarBadge();
  },

  // 更新tabBar徽章
  updateTabBarBadge() {
    const store = useStore();
    const cartCount = store.getCartTotalQuantity();
    
    if (cartCount > 0) {
      wx.setTabBarBadge({
        index: 2, // 购物车tab的索引
        text: cartCount > 99 ? '99+' : cartCount.toString(),
      });
    } else {
      wx.removeTabBarBadge({
        index: 2,
      });
    }
  },
});

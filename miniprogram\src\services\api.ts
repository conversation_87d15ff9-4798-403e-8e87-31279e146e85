import http from './http';
import { 
  User, 
  Product, 
  Category, 
  CartItem, 
  Order, 
  Address, 
  PaymentInfo,
  PaginatedResponse,
  SearchParams,
  LicenseUpload,
  Banner,
  Coupon,
  ApiResponse
} from '../types';

// 认证相关API
export const authApi = {
  // 发送验证码
  sendSms: (phone: string) => 
    http.post('/auth/sms', { phone }),

  // 登录
  login: (phone: string, code: string) => 
    http.post<{ token: string; refresh_token: string; user: User }>('/auth/login', { phone, code }),

  // 刷新token
  refresh: (refreshToken: string) => 
    http.post<{ token: string; refresh_token: string }>('/auth/refresh', { refresh_token: refreshToken }),

  // 登出
  logout: () => 
    http.post('/auth/logout'),
};

// 用户相关API
export const userApi = {
  // 获取用户信息
  getProfile: () => 
    http.get<User>('/users/profile'),

  // 更新用户信息
  updateProfile: (data: Partial<User>) => 
    http.put<User>('/users/profile', data),

  // 上传营业执照
  uploadLicense: (data: LicenseUpload) => 
    http.post('/users/license', data),

  // 获取营业执照状态
  getLicenseStatus: () => 
    http.get<{ status: string; reject_reason?: string }>('/users/license/status'),
};

// 商品相关API
export const productApi = {
  // 获取商品列表
  getProducts: (params: SearchParams) => 
    http.get<PaginatedResponse<Product>>('/products', params),

  // 获取商品详情
  getProduct: (id: string) => 
    http.get<Product>(`/products/${id}`),

  // 搜索商品
  searchProducts: (params: SearchParams) => 
    http.get<PaginatedResponse<Product>>('/products/search', params),

  // 获取热门商品
  getHotProducts: (limit: number = 10) => 
    http.get<Product[]>('/products/hot', { limit }),

  // 获取推荐商品
  getRecommendProducts: (limit: number = 10) => 
    http.get<Product[]>('/products/recommend', { limit }),
};

// 分类相关API
export const categoryApi = {
  // 获取分类列表
  getCategories: () => 
    http.get<Category[]>('/categories'),

  // 获取分类详情
  getCategory: (id: string) => 
    http.get<Category>(`/categories/${id}`),

  // 获取分类下的商品
  getCategoryProducts: (id: string, params: SearchParams) => 
    http.get<PaginatedResponse<Product>>(`/categories/${id}/products`, params),
};

// 购物车相关API
export const cartApi = {
  // 获取购物车
  getCart: () => 
    http.get<CartItem[]>('/cart'),

  // 添加到购物车
  addToCart: (variantId: string, quantity: number) => 
    http.post('/cart', { variant_id: variantId, quantity }),

  // 更新购物车项
  updateCartItem: (id: string, quantity: number) => 
    http.put(`/cart/${id}`, { quantity }),

  // 删除购物车项
  removeCartItem: (id: string) => 
    http.delete(`/cart/${id}`),

  // 清空购物车
  clearCart: () => 
    http.delete('/cart'),

  // 合并购物车（登录后）
  mergeCart: (items: Array<{ variant_id: string; quantity: number }>) => 
    http.post('/cart/merge', { items }),
};

// 地址相关API
export const addressApi = {
  // 获取地址列表
  getAddresses: () => 
    http.get<Address[]>('/addresses'),

  // 获取地址详情
  getAddress: (id: string) => 
    http.get<Address>(`/addresses/${id}`),

  // 创建地址
  createAddress: (data: Omit<Address, 'id' | 'user_id' | 'created_at'>) => 
    http.post<Address>('/addresses', data),

  // 更新地址
  updateAddress: (id: string, data: Partial<Address>) => 
    http.put<Address>(`/addresses/${id}`, data),

  // 删除地址
  deleteAddress: (id: string) => 
    http.delete(`/addresses/${id}`),

  // 设置默认地址
  setDefaultAddress: (id: string) => 
    http.put(`/addresses/${id}/default`),
};

// 订单相关API
export const orderApi = {
  // 获取订单列表
  getOrders: (status?: string, page: number = 1, size: number = 10) => 
    http.get<PaginatedResponse<Order>>('/orders', { status, page, size }),

  // 获取订单详情
  getOrder: (id: string) => 
    http.get<Order>(`/orders/${id}`),

  // 创建订单（下单）
  createOrder: (data: {
    cart_items: Array<{ id: string; quantity: number }>;
    address_id: string;
    coupon_id?: string;
    idempotency_key: string;
  }) => 
    http.post<{ order: Order; payment_info: PaymentInfo }>('/checkout', data),

  // 取消订单
  cancelOrder: (id: string, reason?: string) => 
    http.put(`/orders/${id}/cancel`, { reason }),

  // 确认收货
  confirmOrder: (id: string) => 
    http.put(`/orders/${id}/confirm`),
};

// 支付相关API
export const paymentApi = {
  // 获取支付信息
  getPaymentInfo: (orderId: string) => 
    http.get<PaymentInfo>(`/payments/${orderId}`),

  // 支付结果查询
  queryPaymentResult: (orderId: string) => 
    http.get<{ status: string; paid_at?: string }>(`/payments/${orderId}/result`),
};

// 首页相关API
export const homeApi = {
  // 获取轮播图
  getBanners: () => 
    http.get<Banner[]>('/banners'),

  // 获取首页数据
  getHomeData: () => 
    http.get<{
      banners: Banner[];
      categories: Category[];
      hot_products: Product[];
      recommend_products: Product[];
    }>('/home'),
};

// 优惠券相关API
export const couponApi = {
  // 获取可用优惠券
  getAvailableCoupons: () => 
    http.get<Coupon[]>('/coupons/available'),

  // 获取我的优惠券
  getMyCoupons: (status?: string) => 
    http.get<Coupon[]>('/coupons/my', { status }),

  // 领取优惠券
  claimCoupon: (id: string) => 
    http.post(`/coupons/${id}/claim`),
};

// 文件上传API
export const uploadApi = {
  // 上传图片
  uploadImage: (filePath: string) => 
    http.uploadFile(filePath, 'image'),

  // 获取上传签名（如果使用直传）
  getUploadSignature: (filename: string, contentType: string) => 
    http.post<{ 
      url: string; 
      fields: Record<string, string>; 
      file_url: string; 
    }>('/upload/signature', { filename, content_type: contentType }),
};

// 统计相关API
export const statsApi = {
  // 商品浏览统计
  trackProductView: (productId: string) => 
    http.post('/stats/product-view', { product_id: productId }),

  // 搜索统计
  trackSearch: (keyword: string, results: number) => 
    http.post('/stats/search', { keyword, results }),
};

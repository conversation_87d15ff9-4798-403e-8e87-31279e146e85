Component({
  properties: {
    // 图片数组
    images: {
      type: Array,
      value: [],
    },
    // 图片显示模式
    imageMode: {
      type: String,
      value: 'aspectFill', // scaleToFill | aspectFit | aspectFill | widthFix | heightFix
    },
    // 是否显示指示点
    showDots: {
      type: Boolean,
      value: true,
    },
    // 是否显示自定义指示点
    showCustomDots: {
      type: Boolean,
      value: false,
    },
    // 是否显示图片计数
    showCounter: {
      type: Boolean,
      value: false,
    },
    // 指示点颜色
    dotColor: {
      type: String,
      value: 'rgba(255, 255, 255, 0.5)',
    },
    // 当前指示点颜色
    activeDotColor: {
      type: String,
      value: '#FFFFFF',
    },
    // 是否自动播放
    autoplay: {
      type: Boolean,
      value: false,
    },
    // 自动播放间隔时间
    interval: {
      type: Number,
      value: 3000,
    },
    // 滑动动画时长
    duration: {
      type: Number,
      value: 500,
    },
    // 是否循环播放
    circular: {
      type: Boolean,
      value: true,
    },
    // 是否懒加载
    lazyLoad: {
      type: Boolean,
      value: true,
    },
    // 是否可以预览
    previewable: {
      type: Boolean,
      value: true,
    },
    // 高度
    height: {
      type: String,
      value: '400rpx',
    },
    // 样式类名
    customClass: {
      type: String,
      value: '',
    },
  },

  data: {
    currentIndex: 0,
    imageLoaded: [] as boolean[],
  },

  lifetimes: {
    attached() {
      this.initImageLoadedState();
    },
  },

  observers: {
    'images': function(images: string[]) {
      this.initImageLoadedState();
    },
  },

  methods: {
    // 初始化图片加载状态
    initImageLoadedState() {
      const images = this.data.images as string[];
      const imageLoaded = new Array(images.length).fill(false);
      this.setData({
        imageLoaded,
        currentIndex: 0,
      });
    },

    // 轮播图切换
    onSwiperChange(event: any) {
      const currentIndex = event.detail.current;
      this.setData({
        currentIndex,
      });

      // 触发切换事件
      this.triggerEvent('change', {
        current: currentIndex,
        source: event.detail.source,
      });
    },

    // 点击指示点
    onDotTap(event: any) {
      const index = event.currentTarget.dataset.index;
      this.setData({
        currentIndex: index,
      });

      // 触发点击指示点事件
      this.triggerEvent('dotTap', { index });
    },

    // 图片加载成功
    onImageLoad(event: any) {
      const index = event.currentTarget.dataset.index;
      const imageLoaded = [...this.data.imageLoaded];
      imageLoaded[index] = true;
      
      this.setData({
        imageLoaded,
      });

      // 触发图片加载成功事件
      this.triggerEvent('imageLoad', {
        index,
        src: event.currentTarget.dataset.src,
      });
    },

    // 图片加载失败
    onImageError(event: any) {
      const index = event.currentTarget.dataset.index;
      
      console.error('Image load failed:', {
        index,
        src: event.currentTarget.dataset.src,
      });

      // 触发图片加载失败事件
      this.triggerEvent('imageError', {
        index,
        src: event.currentTarget.dataset.src,
      });
    },

    // 点击图片
    onImageTap(event: any) {
      const index = event.currentTarget.dataset.index;
      const src = event.currentTarget.dataset.src;
      
      // 触发图片点击事件
      this.triggerEvent('imageTap', {
        index,
        src,
        images: this.data.images,
      });

      // 如果可以预览，则预览图片
      if (this.data.previewable) {
        this.previewImages(index);
      }
    },

    // 预览图片
    previewImages(current: number = 0) {
      const images = this.data.images as string[];
      
      if (!images || images.length === 0) {
        return;
      }

      wx.previewImage({
        current: images[current],
        urls: images,
        fail: (error) => {
          console.error('Preview image failed:', error);
        },
      });
    },

    // 获取当前图片
    getCurrentImage() {
      const images = this.data.images as string[];
      const currentIndex = this.data.currentIndex;
      return images[currentIndex] || '';
    },

    // 切换到指定图片
    switchTo(index: number) {
      const images = this.data.images as string[];
      
      if (index >= 0 && index < images.length) {
        this.setData({
          currentIndex: index,
        });
      }
    },

    // 切换到下一张
    next() {
      const images = this.data.images as string[];
      const currentIndex = this.data.currentIndex;
      const nextIndex = (currentIndex + 1) % images.length;
      
      this.switchTo(nextIndex);
    },

    // 切换到上一张
    prev() {
      const images = this.data.images as string[];
      const currentIndex = this.data.currentIndex;
      const prevIndex = (currentIndex - 1 + images.length) % images.length;
      
      this.switchTo(prevIndex);
    },
  },
});

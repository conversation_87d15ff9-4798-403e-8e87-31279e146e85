<view class="image-carousel">
  <swiper 
    class="carousel-swiper"
    indicator-dots="{{showDots}}"
    indicator-color="{{dotColor}}"
    indicator-active-color="{{activeDotColor}}"
    autoplay="{{autoplay}}"
    interval="{{interval}}"
    duration="{{duration}}"
    circular="{{circular}}"
    bindchange="onSwiperChange"
  >
    <swiper-item wx:for="{{images}}" wx:key="index" class="carousel-item">
      <image 
        class="carousel-image" 
        src="{{item}}" 
        mode="{{imageMode}}"
        lazy-load="{{lazyLoad}}"
        bindload="onImageLoad"
        binderror="onImageError"
        bindtap="onImageTap"
        data-index="{{index}}"
        data-src="{{item}}"
      />
      <view wx:if="{{!imageLoaded[index]}}" class="image-placeholder">
        <view class="placeholder-icon">📷</view>
        <view class="placeholder-text">加载中...</view>
      </view>
    </swiper-item>
  </swiper>
  
  <!-- 自定义指示器 -->
  <view wx:if="{{showCustomDots && images.length > 1}}" class="custom-dots">
    <view 
      wx:for="{{images}}" 
      wx:key="index"
      class="custom-dot {{index === currentIndex ? 'active' : ''}}"
      bindtap="onDotTap"
      data-index="{{index}}"
    ></view>
  </view>
  
  <!-- 图片计数 -->
  <view wx:if="{{showCounter && images.length > 1}}" class="image-counter">
    {{currentIndex + 1}} / {{images.length}}
  </view>
</view>

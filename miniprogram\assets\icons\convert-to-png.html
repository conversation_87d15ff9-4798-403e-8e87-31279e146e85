<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG转PNG工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2FA85A;
            text-align: center;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon-preview {
            margin: 10px 0;
        }
        .icon-preview img {
            width: 64px;
            height: 64px;
            margin: 5px;
        }
        .download-btn {
            background: #2FA85A;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .download-btn:hover {
            background: #268A4A;
        }
        .instructions {
            background: #e8f5e8;
            border: 1px solid #2FA85A;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 SVG转PNG工具</h1>
        <p>将SVG图标转换为PNG格式，用于微信小程序TabBar</p>
        
        <div class="icon-grid">
            <div class="icon-item">
                <h3>🏠 首页</h3>
                <div class="icon-preview">
                    <img src="home.svg" alt="首页" id="home-normal-preview">
                    <img src="home-active.svg" alt="首页激活" id="home-active-preview">
                </div>
                <button class="download-btn" onclick="convertAndDownload('home.svg', 'home.png')">下载普通</button>
                <button class="download-btn" onclick="convertAndDownload('home-active.svg', 'home-active.png')">下载激活</button>
            </div>

            <div class="icon-item">
                <h3>📂 分类</h3>
                <div class="icon-preview">
                    <img src="category.svg" alt="分类" id="category-normal-preview">
                    <img src="category-active.svg" alt="分类激活" id="category-active-preview">
                </div>
                <button class="download-btn" onclick="convertAndDownload('category.svg', 'category.png')">下载普通</button>
                <button class="download-btn" onclick="convertAndDownload('category-active.svg', 'category-active.png')">下载激活</button>
            </div>

            <div class="icon-item">
                <h3>🛒 购物车</h3>
                <div class="icon-preview">
                    <img src="cart.svg" alt="购物车" id="cart-normal-preview">
                    <img src="cart-active.svg" alt="购物车激活" id="cart-active-preview">
                </div>
                <button class="download-btn" onclick="convertAndDownload('cart.svg', 'cart.png')">下载普通</button>
                <button class="download-btn" onclick="convertAndDownload('cart-active.svg', 'cart-active.png')">下载激活</button>
            </div>

            <div class="icon-item">
                <h3>👤 我的</h3>
                <div class="icon-preview">
                    <img src="profile.svg" alt="我的" id="profile-normal-preview">
                    <img src="profile-active.svg" alt="我的激活" id="profile-active-preview">
                </div>
                <button class="download-btn" onclick="convertAndDownload('profile.svg', 'profile.png')">下载普通</button>
                <button class="download-btn" onclick="convertAndDownload('profile-active.svg', 'profile-active.png')">下载激活</button>
            </div>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="download-btn" onclick="downloadAllPNG()" style="font-size: 16px; padding: 12px 24px;">
                📦 一键下载所有PNG图标
            </button>
        </div>

        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>点击"下载"按钮将SVG转换为PNG并下载</li>
                <li>将下载的PNG文件放置到 <code>miniprogram/assets/icons/</code> 目录</li>
                <li>更新 <code>app.json</code> 中的tabBar配置</li>
                <li>重新编译小程序即可看到图标效果</li>
            </ol>
            
            <h4>🔧 app.json配置示例</h4>
            <pre style="background: #f0f0f0; padding: 10px; border-radius: 4px; overflow-x: auto;">
"tabBar": {
  "color": "#666666",
  "selectedColor": "#2FA85A",
  "backgroundColor": "#FFFFFF",
  "borderStyle": "black",
  "list": [
    {
      "pagePath": "pages/home/<USER>",
      "text": "首页",
      "iconPath": "assets/icons/home.png",
      "selectedIconPath": "assets/icons/home-active.png"
    },
    {
      "pagePath": "pages/category/category",
      "text": "分类",
      "iconPath": "assets/icons/category.png",
      "selectedIconPath": "assets/icons/category-active.png"
    },
    {
      "pagePath": "pages/cart/cart",
      "text": "购物车",
      "iconPath": "assets/icons/cart.png",
      "selectedIconPath": "assets/icons/cart-active.png"
    },
    {
      "pagePath": "pages/profile/profile",
      "text": "我的",
      "iconPath": "assets/icons/profile.png",
      "selectedIconPath": "assets/icons/profile-active.png"
    }
  ]
}</pre>
        </div>
    </div>

    <canvas id="canvas" width="64" height="64"></canvas>

    <script>
        function convertAndDownload(svgFile, pngFile) {
            const img = new Image();
            img.onload = function() {
                const canvas = document.getElementById('canvas');
                const ctx = canvas.getContext('2d');
                
                // 设置画布大小
                canvas.width = 64;
                canvas.height = 64;
                
                // 清除画布
                ctx.clearRect(0, 0, 64, 64);
                
                // 绘制SVG图像
                ctx.drawImage(img, 0, 0, 64, 64);
                
                // 转换为PNG并下载
                const dataURL = canvas.toDataURL('image/png');
                downloadImage(dataURL, pngFile);
            };
            
            img.onerror = function() {
                alert('无法加载SVG文件: ' + svgFile);
            };
            
            img.src = svgFile;
        }

        function downloadImage(dataUrl, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = dataUrl;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function downloadAllPNG() {
            const icons = [
                { svg: 'home.svg', png: 'home.png' },
                { svg: 'home-active.svg', png: 'home-active.png' },
                { svg: 'category.svg', png: 'category.png' },
                { svg: 'category-active.svg', png: 'category-active.png' },
                { svg: 'cart.svg', png: 'cart.png' },
                { svg: 'cart-active.svg', png: 'cart-active.png' },
                { svg: 'profile.svg', png: 'profile.png' },
                { svg: 'profile-active.svg', png: 'profile-active.png' }
            ];

            icons.forEach((icon, index) => {
                setTimeout(() => {
                    convertAndDownload(icon.svg, icon.png);
                }, index * 500); // 延迟下载，避免浏览器阻止
            });

            alert('开始下载所有PNG图标，请稍等...');
        }

        // 检查SVG文件是否存在
        window.onload = function() {
            const svgFiles = ['home.svg', 'home-active.svg', 'category.svg', 'category-active.svg', 
                             'cart.svg', 'cart-active.svg', 'profile.svg', 'profile-active.svg'];
            
            svgFiles.forEach(file => {
                const img = new Image();
                img.onerror = function() {
                    console.warn('SVG文件不存在或无法加载: ' + file);
                };
                img.src = file;
            });
        };
    </script>
</body>
</html>

<view class="profile-page">
  <!-- 未登录状态 -->
  <view wx:if="{{!user}}" class="login-prompt">
    <view class="login-avatar">👤</view>
    <view class="login-text">登录后享受更多服务</view>
    <button class="login-btn" bindtap="onLogin">立即登录</button>
  </view>

  <!-- 已登录状态 -->
  <view wx:else class="profile-content">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <image 
          class="user-avatar" 
          src="{{user.avatar || '/assets/icons/default-avatar.png'}}" 
          mode="aspectFill"
        />
        <view class="user-details">
          <view class="user-name">{{user.nickname || user.phone}}</view>
          <view class="user-role">
            <text class="role-badge {{user.role === 'B' ? 'business' : 'consumer'}}">
              {{user.role === 'B' ? 'B端用户' : 'C端用户'}}
            </text>
          </view>
          <view wx:if="{{user.role === 'C'}}" class="upgrade-tip">
            <text class="upgrade-text">上传营业执照升级B端，享受更低价格</text>
            <text class="upgrade-arrow">›</text>
          </view>
        </view>
      </view>
      
      <!-- B端用户状态 -->
      <view wx:if="{{user.role === 'B' || user.license_status !== 'none'}}" class="license-status">
        <view class="status-item">
          <text class="status-label">营业执照状态：</text>
          <text class="status-value {{licenseStatusClass}}">{{licenseStatusText}}</text>
        </view>
        <view wx:if="{{user.license_status === 'rejected'}}" class="reject-reason">
          拒绝原因：{{user.license_reject_reason}}
        </view>
      </view>
    </view>

    <!-- 订单统计 -->
    <view class="order-stats">
      <view class="stats-title">我的订单</view>
      <view class="stats-grid">
        <view class="stats-item" bindtap="onOrderTap" data-status="">
          <view class="stats-icon">📋</view>
          <view class="stats-text">全部订单</view>
        </view>
        <view class="stats-item" bindtap="onOrderTap" data-status="pending">
          <view class="stats-icon">💰</view>
          <view class="stats-text">待付款</view>
          <view wx:if="{{orderStats.pending > 0}}" class="stats-badge">{{orderStats.pending}}</view>
        </view>
        <view class="stats-item" bindtap="onOrderTap" data-status="shipped">
          <view class="stats-icon">🚚</view>
          <view class="stats-text">待收货</view>
          <view wx:if="{{orderStats.shipped > 0}}" class="stats-badge">{{orderStats.shipped}}</view>
        </view>
        <view class="stats-item" bindtap="onOrderTap" data-status="delivered">
          <view class="stats-icon">⭐</view>
          <view class="stats-text">待评价</view>
          <view wx:if="{{orderStats.delivered > 0}}" class="stats-badge">{{orderStats.delivered}}</view>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" bindtap="onAddressTap">
          <view class="menu-icon">📍</view>
          <view class="menu-text">收货地址</view>
          <view class="menu-arrow">›</view>
        </view>
        
        <view wx:if="{{user.role === 'C'}}" class="menu-item" bindtap="onLicenseTap">
          <view class="menu-icon">📄</view>
          <view class="menu-text">营业执照上传</view>
          <view class="menu-arrow">›</view>
        </view>
        
        <view wx:if="{{user.role === 'B'}}" class="menu-item" bindtap="onApiTokenTap">
          <view class="menu-icon">🔑</view>
          <view class="menu-text">API Token</view>
          <view class="menu-arrow">›</view>
        </view>
      </view>

      <view class="menu-group">
        <view class="menu-item" bindtap="onFeedbackTap">
          <view class="menu-icon">💬</view>
          <view class="menu-text">意见反馈</view>
          <view class="menu-arrow">›</view>
        </view>
        
        <view class="menu-item" bindtap="onAboutTap">
          <view class="menu-icon">ℹ️</view>
          <view class="menu-text">关于我们</view>
          <view class="menu-arrow">›</view>
        </view>
        
        <view class="menu-item" bindtap="onSettingsTap">
          <view class="menu-icon">⚙️</view>
          <view class="menu-text">设置</view>
          <view class="menu-arrow">›</view>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" bindtap="onLogout">退出登录</button>
    </view>
  </view>

  <!-- 客服联系 -->
  <view class="contact-section">
    <view class="contact-item" bindtap="onCallService">
      <view class="contact-icon">📞</view>
      <view class="contact-text">客服电话：400-123-4567</view>
    </view>
    <view class="contact-item" bindtap="onWechatService">
      <view class="contact-icon">💬</view>
      <view class="contact-text">微信客服</view>
    </view>
  </view>
</view>

<view class="login-page">
  <view class="login-container">
    <!-- Logo和标题 -->
    <view class="header">
      <view class="logo">
        <text class="logo-text">刀刀乐</text>
      </view>
      <view class="subtitle">捡漏网 - 发现超值好货</view>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <!-- 手机号输入 -->
      <view class="input-group">
        <view class="input-label">手机号</view>
        <input 
          class="input-field"
          type="number"
          placeholder="请输入手机号"
          value="{{phone}}"
          bindinput="onPhoneInput"
          maxlength="11"
        />
      </view>

      <!-- 验证码输入 -->
      <view class="input-group">
        <view class="input-label">验证码</view>
        <view class="code-input-container">
          <input 
            class="input-field code-input"
            type="number"
            placeholder="请输入验证码"
            value="{{code}}"
            bindinput="onCodeInput"
            maxlength="6"
          />
          <button 
            class="send-code-btn {{canSendCode ? '' : 'disabled'}}"
            bindtap="onSendCode"
            disabled="{{!canSendCode}}"
          >
            {{sendCodeText}}
          </button>
        </view>
      </view>

      <!-- 登录按钮 -->
      <button 
        class="login-btn {{canLogin ? '' : 'disabled'}}"
        bindtap="onLogin"
        disabled="{{!canLogin || logging}}"
      >
        <text wx:if="{{logging}}">登录中...</text>
        <text wx:else>登录</text>
      </button>

      <!-- 协议同意 -->
      <view class="agreement">
        <checkbox-group bindchange="onAgreementChange">
          <label class="agreement-item">
            <checkbox value="agree" checked="{{agreed}}" />
            <text class="agreement-text">
              我已阅读并同意
              <text class="link" bindtap="onViewUserAgreement">《用户协议》</text>
              和
              <text class="link" bindtap="onViewPrivacyPolicy">《隐私政策》</text>
            </text>
          </label>
        </checkbox-group>
      </view>
    </view>

    <!-- 其他登录方式 -->
    <view class="other-login" wx:if="{{false}}">
      <view class="divider">
        <view class="divider-line"></view>
        <text class="divider-text">其他登录方式</text>
        <view class="divider-line"></view>
      </view>
      
      <view class="social-login">
        <button class="social-btn wechat" open-type="getPhoneNumber" bindgetphonenumber="onGetPhoneNumber">
          <text class="social-icon">📱</text>
          <text class="social-text">微信授权登录</text>
        </button>
      </view>
    </view>

    <!-- 底部提示 -->
    <view class="footer-tips">
      <text class="tip-text">登录即表示您同意我们的服务条款</text>
      <text class="tip-text">如有问题，请联系客服：************</text>
    </view>
  </view>
</view>

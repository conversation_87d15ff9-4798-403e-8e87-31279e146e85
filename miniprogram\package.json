{"name": "daodaole-miniprogram", "version": "1.0.0", "description": "刀刀乐捡漏网微信小程序", "main": "app.js", "scripts": {"dev": "tsc --watch", "build": "tsc", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "test": "jest", "mock": "json-server --watch mock/db.json --port 3000"}, "keywords": ["miniprogram", "wechat", "typescript"], "author": "da<PERSON><PERSON>", "license": "MIT", "devDependencies": {"@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.0.0", "json-server": "^0.17.0", "miniprogram-api-typings": "^3.12.0", "prettier": "^2.0.0", "typescript": "^4.9.0"}, "dependencies": {}}
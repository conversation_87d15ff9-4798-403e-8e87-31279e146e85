# 部署指南

本文档介绍如何部署刀刀乐捡漏网小程序到不同环境。

## 开发环境部署

### 1. 环境准备

确保已安装以下软件：
- Node.js (v16+)
- 微信开发者工具
- Git

### 2. 快速启动

#### Windows 用户
```bash
# 双击运行启动脚本
start.bat
```

#### macOS/Linux 用户
```bash
# 进入项目目录
cd miniprogram

# 安装依赖
npm install

# 启动 Mock 服务器
npm run mock &

# 编译 TypeScript
npm run dev
```

### 3. 微信开发者工具配置

1. 打开微信开发者工具
2. 选择"导入项目"
3. 项目目录选择本项目根目录
4. AppID 填写测试号或正式 AppID
5. 点击"导入"

### 4. 开发环境验证

- Mock 服务器运行在 http://localhost:3000
- 在微信开发者工具中可以正常预览小程序
- 可以正常登录、浏览商品、添加购物车等功能

## 测试环境部署

### 1. 修改配置

在 `src/services/http.ts` 中修改 API 基础地址：

```typescript
const ENV_CONFIG = {
  development: {
    baseURL: 'https://test-api.daodaole.com/api/v1',
  },
  // ...
};
```

### 2. 编译和上传

```bash
# 编译 TypeScript
npm run build

# 在微信开发者工具中上传代码
# 工具 -> 上传 -> 填写版本号和项目备注
```

### 3. 配置体验版

1. 登录微信公众平台
2. 进入"开发管理" -> "开发版本"
3. 选择刚上传的版本，点击"提交审核"前的"设为体验版"
4. 添加体验成员

## 生产环境部署

### 1. 环境配置

#### API 地址配置
```typescript
const ENV_CONFIG = {
  production: {
    baseURL: 'https://api.daodaole.com/api/v1',
  },
  // ...
};
```

#### 域名配置
在微信公众平台配置以下域名：

**服务器域名 (request 合法域名)**
- https://api.daodaole.com

**上传文件域名 (uploadFile 合法域名)**
- https://api.daodaole.com

**下载文件域名 (downloadFile 合法域名)**
- https://api.daodaole.com
- https://cdn.daodaole.com

### 2. 支付配置

#### 微信支付配置
1. 在微信公众平台绑定微信支付商户号
2. 配置支付目录：https://api.daodaole.com/
3. 在后端配置支付密钥和证书

#### 小程序支付权限
确保小程序已开通微信支付功能

### 3. 图标资源

准备以下图标文件并放置在 `assets/icons/` 目录：

- `home.png` / `home-active.png` - 首页图标
- `category.png` / `category-active.png` - 分类图标  
- `cart.png` / `cart-active.png` - 购物车图标
- `profile.png` / `profile-active.png` - 个人中心图标

图标规格：64x64px 或 128x128px，PNG 格式

### 4. 隐私协议配置

#### 用户协议和隐私政策
1. 准备用户协议和隐私政策页面
2. 部署到 web 服务器
3. 在登录页面中更新链接地址

#### 小程序隐私保护指引
在微信公众平台配置隐私保护指引，说明收集的用户信息类型和用途

### 5. 发布流程

#### 代码上传
```bash
# 最终编译
npm run build

# 代码检查
npm run lint

# 在微信开发者工具中上传代码
```

#### 提交审核
1. 登录微信公众平台
2. 进入"开发管理" -> "开发版本"
3. 选择要发布的版本
4. 点击"提交审核"
5. 填写审核信息：
   - 功能页面：首页、商品详情、购物车、个人中心等
   - 功能描述：电商购物小程序，支持商品浏览、购买、支付等
   - 测试账号：提供测试用的手机号和验证码

#### 发布上线
1. 审核通过后，在"开发管理" -> "审核版本"中点击"发布"
2. 发布后用户即可在微信中搜索到小程序

## 监控和维护

### 1. 性能监控

#### 小程序性能监控
- 在微信公众平台查看"运维中心" -> "性能监控"
- 关注页面加载时间、接口响应时间等指标

#### 错误监控
- 集成第三方错误监控服务（如 Sentry）
- 在 `app.ts` 的 `onError` 中上报错误

### 2. 数据统计

#### 微信官方统计
- 在微信公众平台查看"统计" -> "用户分析"、"页面分析"等

#### 自定义统计
- 在关键页面和操作中添加埋点
- 统计商品浏览、购买转化率等业务指标

### 3. 版本管理

#### 版本号规范
使用语义化版本号：`主版本号.次版本号.修订号`

- 主版本号：不兼容的 API 修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

#### 发布记录
维护版本发布记录，包括：
- 版本号和发布时间
- 新增功能和修复问题
- 已知问题和注意事项

## 常见问题

### 1. 域名配置问题

**问题**：请求失败，提示域名不在合法域名列表中
**解决**：在微信公众平台配置 request 合法域名

### 2. 支付问题

**问题**：支付失败，提示商户号配置错误
**解决**：检查微信支付商户号绑定和配置

### 3. 图片加载问题

**问题**：图片无法显示
**解决**：检查图片域名是否在 downloadFile 合法域名中

### 4. 登录问题

**问题**：无法获取用户手机号
**解决**：确保小程序已认证，且使用 button 组件的 open-type="getPhoneNumber"

## 技术支持

如遇到部署问题，请联系：
- 技术支持邮箱：<EMAIL>
- 微信群：扫描二维码加入技术交流群

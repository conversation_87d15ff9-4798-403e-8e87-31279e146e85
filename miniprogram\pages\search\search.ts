import { Product, User, SearchParams } from '../../src/types';
import { productApi, statsApi } from '../../src/services/api';
import { formatPrice, debounce } from '../../src/utils';
import { useStore } from '../../src/store';

interface SearchData {
  keyword: string;
  suggestions: string[];
  showSuggestions: boolean;
  searchHistory: string[];
  hotKeywords: string[];
  products: (Product & { displayPrice: string })[];
  recommendProducts: (Product & { displayPrice: string })[];
  totalCount: number;
  currentSort: string;
  loading: boolean;
  hasSearched: boolean;
  hasMore: boolean;
  page: number;
  sortOptions: Array<{ label: string; value: string }>;
}

Page<SearchData, {}>({
  data: {
    keyword: '',
    suggestions: [],
    showSuggestions: false,
    searchHistory: [],
    hotKeywords: ['iPhone', '运动鞋', '化妆品', '家居用品', '数码配件', '服装', '食品', '图书'],
    products: [],
    recommendProducts: [],
    totalCount: 0,
    currentSort: 'default',
    loading: false,
    hasSearched: false,
    hasMore: true,
    page: 1,
    sortOptions: [
      { label: '综合', value: 'default' },
      { label: '价格', value: 'price_asc' },
      { label: '销量', value: 'sales_desc' },
      { label: '最新', value: 'created_desc' },
    ],
  },

  onLoad(options: any) {
    // 从URL参数获取搜索关键词
    const keyword = options.q || '';
    const type = options.type; // 'hot' 表示热门商品
    
    if (keyword) {
      this.setData({
        keyword,
        hasSearched: true,
      });
      this.performSearch();
    } else if (type === 'hot') {
      this.setData({
        keyword: '热门商品',
        hasSearched: true,
      });
      this.loadHotProducts();
    }

    this.loadSearchHistory();
    this.loadRecommendProducts();
    
    // 创建防抖搜索函数
    this.debouncedSearch = debounce(this.getSuggestions.bind(this), 300);
  },

  onShow() {
    // 更新购物车徽章
    this.updateTabBarBadge();
  },

  // 加载搜索历史
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync('searchHistory') || [];
      this.setData({
        searchHistory: history.slice(0, 10), // 最多显示10条
      });
    } catch (error) {
      console.error('Failed to load search history:', error);
    }
  },

  // 保存搜索历史
  saveSearchHistory(keyword: string) {
    try {
      let history = wx.getStorageSync('searchHistory') || [];
      
      // 移除重复项
      history = history.filter((item: string) => item !== keyword);
      
      // 添加到开头
      history.unshift(keyword);
      
      // 限制数量
      history = history.slice(0, 20);
      
      wx.setStorageSync('searchHistory', history);
      this.setData({
        searchHistory: history.slice(0, 10),
      });
    } catch (error) {
      console.error('Failed to save search history:', error);
    }
  },

  // 加载推荐商品
  async loadRecommendProducts() {
    try {
      const response = await productApi.getRecommendProducts(6);
      const store = useStore();
      const user = store.getUser();
      
      const formattedProducts = response.data.map(product => ({
        ...product,
        displayPrice: formatPrice(user?.role === 'B' ? product.price_b : product.price_c),
      }));

      this.setData({
        recommendProducts: formattedProducts,
      });
    } catch (error) {
      console.error('Failed to load recommend products:', error);
    }
  },

  // 关键词输入
  onKeywordInput(event: any) {
    const keyword = event.detail.value;
    this.setData({
      keyword,
      showSuggestions: keyword.length > 0,
    });

    if (keyword.length > 0) {
      this.debouncedSearch(keyword);
    } else {
      this.setData({
        suggestions: [],
        showSuggestions: false,
      });
    }
  },

  // 获取搜索建议
  async getSuggestions(keyword: string) {
    // 这里可以调用搜索建议接口
    // 暂时使用模拟数据
    const mockSuggestions = [
      `${keyword} 手机`,
      `${keyword} 配件`,
      `${keyword} 保护套`,
      `${keyword} 充电器`,
    ].filter(item => item !== keyword);

    this.setData({
      suggestions: mockSuggestions.slice(0, 5),
    });
  },

  // 清除关键词
  onClearKeyword() {
    this.setData({
      keyword: '',
      suggestions: [],
      showSuggestions: false,
    });
  },

  // 点击搜索建议
  onSuggestionTap(event: any) {
    const keyword = event.currentTarget.dataset.keyword;
    this.setData({
      keyword,
      showSuggestions: false,
    });
    this.performSearch();
  },

  // 点击搜索历史
  onHistoryTap(event: any) {
    const keyword = event.currentTarget.dataset.keyword;
    this.setData({
      keyword,
    });
    this.performSearch();
  },

  // 清空搜索历史
  onClearHistory() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('searchHistory');
          this.setData({
            searchHistory: [],
          });
        }
      },
    });
  },

  // 点击热门关键词
  onHotKeywordTap(event: any) {
    const keyword = event.currentTarget.dataset.keyword;
    this.setData({
      keyword,
    });
    this.performSearch();
  },

  // 执行搜索
  onSearch() {
    const { keyword } = this.data;
    if (!keyword.trim()) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none',
      });
      return;
    }

    this.performSearch();
  },

  // 执行搜索逻辑
  async performSearch(reset: boolean = true) {
    const { keyword, currentSort, page } = this.data;
    
    if (!keyword.trim()) return;

    this.setData({ 
      loading: true,
      showSuggestions: false,
    });

    try {
      const params: SearchParams = {
        q: keyword.trim(),
        sort: currentSort as any,
        page: reset ? 1 : page,
        size: 20,
      };

      const response = await productApi.searchProducts(params);
      const { items, total } = response.data;
      
      const store = useStore();
      const user = store.getUser();
      
      // 格式化商品价格
      const formattedProducts = items.map(product => ({
        ...product,
        displayPrice: formatPrice(user?.role === 'B' ? product.price_b : product.price_c),
      }));

      this.setData({
        products: reset ? formattedProducts : [...this.data.products, ...formattedProducts],
        totalCount: total,
        hasSearched: true,
        hasMore: items.length >= 20,
        page: reset ? 2 : page + 1,
      });

      // 保存搜索历史
      if (reset) {
        this.saveSearchHistory(keyword.trim());
        
        // 统计搜索行为
        this.trackSearch(keyword.trim(), total);
      }

    } catch (error) {
      console.error('Search failed:', error);
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none',
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载热门商品
  async loadHotProducts() {
    this.setData({ loading: true });

    try {
      const response = await productApi.getHotProducts(20);
      const store = useStore();
      const user = store.getUser();
      
      const formattedProducts = response.data.map(product => ({
        ...product,
        displayPrice: formatPrice(user?.role === 'B' ? product.price_b : product.price_c),
      }));

      this.setData({
        products: formattedProducts,
        totalCount: formattedProducts.length,
        hasSearched: true,
        hasMore: false,
      });

    } catch (error) {
      console.error('Failed to load hot products:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 点击排序
  onSortTap(event: any) {
    const sort = event.currentTarget.dataset.sort;
    
    if (sort === this.data.currentSort) {
      return;
    }

    this.setData({
      currentSort: sort,
      products: [],
      page: 1,
      hasMore: true,
    });

    this.performSearch();
  },

  // 加载更多
  onLoadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.performSearch(false);
    }
  },

  // 点击商品
  onProductTap(event: any) {
    const { product } = event.detail;
    wx.navigateTo({
      url: `/pages/product/product?id=${product.id}`,
    });
  },

  // 点击推荐商品
  onRecommendTap(event: any) {
    const product = event.currentTarget.dataset.product;
    wx.navigateTo({
      url: `/pages/product/product?id=${product.id}`,
    });
  },

  // 添加到购物车
  onAddToCart(event: any) {
    const { product } = event.detail;
    console.log('Added to cart:', product);
    
    // 更新购物车徽章
    this.updateTabBarBadge();
  },

  // 统计搜索行为
  async trackSearch(keyword: string, results: number) {
    try {
      await statsApi.trackSearch(keyword, results);
    } catch (error) {
      console.error('Failed to track search:', error);
    }
  },

  // 更新tabBar徽章
  updateTabBarBadge() {
    const store = useStore();
    const cartCount = store.getCartTotalQuantity();
    
    if (cartCount > 0) {
      wx.setTabBarBadge({
        index: 2, // 购物车tab的索引
        text: cartCount > 99 ? '99+' : cartCount.toString(),
      });
    } else {
      wx.removeTabBarBadge({
        index: 2,
      });
    }
  },

  // 防抖搜索函数（在onLoad中初始化）
  debouncedSearch: null as any,
});

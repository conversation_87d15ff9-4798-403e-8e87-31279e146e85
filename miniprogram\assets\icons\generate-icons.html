<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标生成器 - 刀刀乐捡漏网</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2FA85A;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .icon-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #fafafa;
        }
        .icon-preview {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 15px 0;
        }
        .icon-box {
            width: 64px;
            height: 64px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            position: relative;
        }
        .icon-normal {
            background-color: #666666;
        }
        .icon-active {
            background-color: #2FA85A;
        }
        .icon-label {
            font-size: 12px;
            margin-top: 5px;
            color: #666;
        }
        .download-btn {
            background: #2FA85A;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .download-btn:hover {
            background: #268A4A;
        }
        .instructions {
            background: #e8f5e8;
            border: 1px solid #2FA85A;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        .instructions h3 {
            color: #2FA85A;
            margin-top: 0;
        }
        canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ 刀刀乐捡漏网 - TabBar图标生成器</h1>
        
        <div class="icon-grid">
            <div class="icon-item">
                <h3>🏠 首页</h3>
                <div class="icon-preview">
                    <div>
                        <div class="icon-box icon-normal" id="home-normal">🏠</div>
                        <div class="icon-label">未选中</div>
                    </div>
                    <div>
                        <div class="icon-box icon-active" id="home-active">🏠</div>
                        <div class="icon-label">选中</div>
                    </div>
                </div>
                <button class="download-btn" onclick="downloadIcon('home', '🏠')">下载图标</button>
            </div>

            <div class="icon-item">
                <h3>📂 分类</h3>
                <div class="icon-preview">
                    <div>
                        <div class="icon-box icon-normal" id="category-normal">📂</div>
                        <div class="icon-label">未选中</div>
                    </div>
                    <div>
                        <div class="icon-box icon-active" id="category-active">📂</div>
                        <div class="icon-label">选中</div>
                    </div>
                </div>
                <button class="download-btn" onclick="downloadIcon('category', '📂')">下载图标</button>
            </div>

            <div class="icon-item">
                <h3>🛒 购物车</h3>
                <div class="icon-preview">
                    <div>
                        <div class="icon-box icon-normal" id="cart-normal">🛒</div>
                        <div class="icon-label">未选中</div>
                    </div>
                    <div>
                        <div class="icon-box icon-active" id="cart-active">🛒</div>
                        <div class="icon-label">选中</div>
                    </div>
                </div>
                <button class="download-btn" onclick="downloadIcon('cart', '🛒')">下载图标</button>
            </div>

            <div class="icon-item">
                <h3>👤 我的</h3>
                <div class="icon-preview">
                    <div>
                        <div class="icon-box icon-normal" id="profile-normal">👤</div>
                        <div class="icon-label">未选中</div>
                    </div>
                    <div>
                        <div class="icon-box icon-active" id="profile-active">👤</div>
                        <div class="icon-label">选中</div>
                    </div>
                </div>
                <button class="download-btn" onclick="downloadIcon('profile', '👤')">下载图标</button>
            </div>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="download-btn" onclick="downloadAllIcons()" style="font-size: 16px; padding: 12px 24px;">
                📦 一键下载所有图标
            </button>
        </div>

        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>点击"下载图标"按钮下载单个图标，或点击"一键下载所有图标"</li>
                <li>将下载的PNG文件放置到 <code>miniprogram/assets/icons/</code> 目录</li>
                <li>更新 <code>app.json</code> 中的tabBar配置，添加iconPath和selectedIconPath</li>
                <li>重新编译小程序即可看到图标效果</li>
            </ol>
            
            <h4>🎨 自定义图标</h4>
            <p>如需更专业的图标，推荐使用以下开源工具：</p>
            <ul>
                <li><strong>Tabler Icons</strong>: <a href="https://tabler-icons.io/" target="_blank">https://tabler-icons.io/</a></li>
                <li><strong>Heroicons</strong>: <a href="https://heroicons.com/" target="_blank">https://heroicons.com/</a></li>
                <li><strong>Feather Icons</strong>: <a href="https://feathericons.com/" target="_blank">https://feathericons.com/</a></li>
                <li><strong>Lucide</strong>: <a href="https://lucide.dev/" target="_blank">https://lucide.dev/</a></li>
            </ul>
        </div>
    </div>

    <canvas id="canvas" width="128" height="128"></canvas>

    <script>
        function createIcon(emoji, color, size = 128) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // 设置画布大小
            canvas.width = size;
            canvas.height = size;
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 设置背景（可选）
            ctx.fillStyle = color;
            ctx.fillRect(0, 0, size, size);
            
            // 绘制emoji
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = 'white';
            ctx.fillText(emoji, size / 2, size / 2);
            
            return canvas.toDataURL('image/png');
        }

        function downloadIcon(name, emoji) {
            // 生成未选中状态图标
            const normalIcon = createIcon(emoji, '#666666');
            downloadImage(normalIcon, `${name}.png`);
            
            // 生成选中状态图标
            const activeIcon = createIcon(emoji, '#2FA85A');
            downloadImage(activeIcon, `${name}-active.png`);
        }

        function downloadImage(dataUrl, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = dataUrl;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function downloadAllIcons() {
            const icons = [
                { name: 'home', emoji: '🏠' },
                { name: 'category', emoji: '📂' },
                { name: 'cart', emoji: '🛒' },
                { name: 'profile', emoji: '👤' }
            ];

            icons.forEach((icon, index) => {
                setTimeout(() => {
                    downloadIcon(icon.name, icon.emoji);
                }, index * 500); // 延迟下载，避免浏览器阻止
            });

            alert('开始下载所有图标，请稍等...');
        }

        // 页面加载完成后的提示
        window.onload = function() {
            console.log('图标生成器已加载完成');
        };
    </script>
</body>
</html>

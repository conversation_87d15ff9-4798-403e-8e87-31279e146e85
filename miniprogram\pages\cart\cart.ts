import { CartItem, Product, User } from '../../src/types';
import { productApi } from '../../src/services/api';
import { formatPrice, showConfirm } from '../../src/utils';
import { useStore } from '../../src/store';

interface CartData {
  cartItems: (CartItem & { displayPrice: string })[];
  recommendProducts: (Product & { displayPrice: string })[];
  allSelected: boolean;
  selectedCount: number;
  totalPrice: string;
  loading: boolean;
}

Page<CartData, {}>({
  data: {
    cartItems: [],
    recommendProducts: [],
    allSelected: false,
    selectedCount: 0,
    totalPrice: '0.00',
    loading: false,
  },

  onLoad() {
    this.loadCartData();
    this.loadRecommendProducts();
  },

  onShow() {
    // 每次显示时刷新购物车数据
    this.loadCartData();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  // 加载购物车数据
  loadCartData() {
    const store = useStore();
    const cartItems = store.getCart();
    const user = store.getUser();
    
    // 格式化价格显示
    const formattedItems = cartItems.map(item => ({
      ...item,
      displayPrice: formatPrice(item.price),
    }));

    this.setData({
      cartItems: formattedItems,
    });

    this.updateCartSummary();
  },

  // 加载推荐商品
  async loadRecommendProducts() {
    try {
      const response = await productApi.getRecommendProducts(5);
      const store = useStore();
      const user = store.getUser();
      
      const formattedProducts = response.data.map(product => ({
        ...product,
        displayPrice: formatPrice(user?.role === 'B' ? product.price_b : product.price_c),
      }));

      this.setData({
        recommendProducts: formattedProducts,
      });
    } catch (error) {
      console.error('Failed to load recommend products:', error);
    }
  },

  // 刷新数据
  async refreshData() {
    this.loadCartData();
    await this.loadRecommendProducts();
    wx.stopPullDownRefresh();
  },

  // 更新购物车汇总信息
  updateCartSummary() {
    const { cartItems } = this.data;
    const selectedItems = cartItems.filter(item => item.selected);
    const selectedCount = selectedItems.reduce((sum, item) => sum + item.quantity, 0);
    const totalPrice = selectedItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const allSelected = cartItems.length > 0 && selectedItems.length === cartItems.length;

    this.setData({
      selectedCount,
      totalPrice: formatPrice(totalPrice),
      allSelected,
    });
  },

  // 全选/取消全选
  onSelectAll() {
    const store = useStore();
    const allSelected = !this.data.allSelected;
    
    store.toggleAllCartItems(allSelected);
    this.loadCartData();
  },

  // 选择单个商品
  onSelectItem(event: any) {
    const variantId = event.currentTarget.dataset.variantId;
    const store = useStore();
    
    store.toggleCartItemSelected(variantId);
    this.loadCartData();
  },

  // 减少数量
  onDecreaseQuantity(event: any) {
    const variantId = event.currentTarget.dataset.variantId;
    const item = this.data.cartItems.find(item => item.variant_id === variantId);
    
    if (!item || item.quantity <= 1) {
      return;
    }

    const store = useStore();
    store.updateCartItemQuantity(variantId, item.quantity - 1);
    this.loadCartData();
    
    // 同步到服务器
    this.syncToServer();
  },

  // 增加数量
  onIncreaseQuantity(event: any) {
    const variantId = event.currentTarget.dataset.variantId;
    const item = this.data.cartItems.find(item => item.variant_id === variantId);
    
    if (!item || item.quantity >= item.stock) {
      wx.showToast({
        title: '库存不足',
        icon: 'none',
      });
      return;
    }

    const store = useStore();
    store.updateCartItemQuantity(variantId, item.quantity + 1);
    this.loadCartData();
    
    // 同步到服务器
    this.syncToServer();
  },

  // 数量输入
  onQuantityInput(event: any) {
    const variantId = event.currentTarget.dataset.variantId;
    const quantity = parseInt(event.detail.value) || 1;
    const item = this.data.cartItems.find(item => item.variant_id === variantId);
    
    if (!item) return;

    // 限制数量范围
    const finalQuantity = Math.max(1, Math.min(quantity, item.stock));
    
    if (finalQuantity !== quantity) {
      wx.showToast({
        title: finalQuantity === item.stock ? '库存不足' : '数量不能小于1',
        icon: 'none',
      });
    }

    const store = useStore();
    store.updateCartItemQuantity(variantId, finalQuantity);
    this.loadCartData();
    
    // 同步到服务器
    this.syncToServer();
  },

  // 删除商品
  async onDeleteItem(event: any) {
    const variantId = event.currentTarget.dataset.variantId;
    const item = this.data.cartItems.find(item => item.variant_id === variantId);
    
    if (!item) return;

    const confirmed = await showConfirm(
      '确认删除',
      `确定要删除"${item.product_name}"吗？`
    );

    if (confirmed) {
      const store = useStore();
      store.removeCartItem(variantId);
      this.loadCartData();
      
      wx.showToast({
        title: '已删除',
        icon: 'success',
      });
      
      // 同步到服务器
      this.syncToServer();
    }
  },

  // 点击商品项
  onItemTap(event: any) {
    const item = event.currentTarget.dataset.item;
    wx.navigateTo({
      url: `/pages/product/product?id=${item.product_id}`,
    });
  },

  // 点击推荐商品
  onRecommendTap(event: any) {
    const product = event.currentTarget.dataset.product;
    wx.navigateTo({
      url: `/pages/product/product?id=${product.id}`,
    });
  },

  // 去逛逛
  onGoShopping() {
    wx.switchTab({
      url: '/pages/home/<USER>',
    });
  },

  // 去结算
  onCheckout() {
    const { selectedCount } = this.data;
    
    if (selectedCount === 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none',
      });
      return;
    }

    // 检查登录状态
    const store = useStore();
    const user = store.getUser();
    
    if (!user) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
      });
      
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login?redirect=' + encodeURIComponent('/pages/cart/cart'),
        });
      }, 1500);
      return;
    }

    // 检查库存
    const selectedItems = this.data.cartItems.filter(item => item.selected);
    const outOfStockItems = selectedItems.filter(item => item.quantity > item.stock);
    
    if (outOfStockItems.length > 0) {
      wx.showToast({
        title: '部分商品库存不足，请调整数量',
        icon: 'none',
      });
      return;
    }

    // 跳转到结算页
    wx.navigateTo({
      url: '/pages/checkout/checkout',
    });
  },

  // 同步到服务器
  async syncToServer() {
    const store = useStore();
    const user = store.getUser();
    
    if (user) {
      try {
        await store.syncCartFromServer();
      } catch (error) {
        console.error('Failed to sync cart to server:', error);
      }
    }
  },
});

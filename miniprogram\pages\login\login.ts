import { authApi } from '../../src/services/api';
import { validatePhone } from '../../src/utils';
import { useStore } from '../../src/store';

interface LoginData {
  phone: string;
  code: string;
  agreed: boolean;
  canSendCode: boolean;
  canLogin: boolean;
  logging: boolean;
  sendCodeText: string;
  countdown: number;
}

Page<LoginData, {}>({
  data: {
    phone: '',
    code: '',
    agreed: false,
    canSendCode: false,
    canLogin: false,
    logging: false,
    sendCodeText: '发送验证码',
    countdown: 0,
  },

  onLoad(options: any) {
    // 如果从其他页面跳转过来，可能携带重定向信息
    if (options.redirect) {
      this.redirectUrl = decodeURIComponent(options.redirect);
    }
  },

  onShow() {
    // 检查是否已经登录
    const store = useStore();
    const user = store.getUser();
    
    if (user) {
      this.redirectAfterLogin();
    }
  },

  // 手机号输入
  onPhoneInput(event: any) {
    const phone = event.detail.value;
    this.setData({
      phone,
      canSendCode: validatePhone(phone),
    });
    this.updateCanLogin();
  },

  // 验证码输入
  onCodeInput(event: any) {
    const code = event.detail.value;
    this.setData({
      code,
    });
    this.updateCanLogin();
  },

  // 更新是否可以登录
  updateCanLogin() {
    const { phone, code, agreed } = this.data;
    const canLogin = validatePhone(phone) && code.length >= 4 && agreed;
    this.setData({
      canLogin,
    });
  },

  // 协议同意状态变化
  onAgreementChange(event: any) {
    const agreed = event.detail.value.includes('agree');
    this.setData({
      agreed,
    });
    this.updateCanLogin();
  },

  // 发送验证码
  async onSendCode() {
    if (!this.data.canSendCode || this.data.countdown > 0) {
      return;
    }

    const { phone } = this.data;
    
    if (!validatePhone(phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none',
      });
      return;
    }

    try {
      wx.showLoading({ title: '发送中...' });
      
      await authApi.sendSms(phone);
      
      wx.showToast({
        title: '验证码已发送',
        icon: 'success',
      });
      
      // 开始倒计时
      this.startCountdown();
      
    } catch (error: any) {
      console.error('Send SMS failed:', error);
      wx.showToast({
        title: error.message || '发送失败，请重试',
        icon: 'none',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60;
    this.setData({
      countdown,
      canSendCode: false,
      sendCodeText: `${countdown}s后重发`,
    });

    const timer = setInterval(() => {
      countdown--;
      
      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({
          countdown: 0,
          canSendCode: validatePhone(this.data.phone),
          sendCodeText: '重新发送',
        });
      } else {
        this.setData({
          countdown,
          sendCodeText: `${countdown}s后重发`,
        });
      }
    }, 1000);
  },

  // 登录
  async onLogin() {
    if (!this.data.canLogin || this.data.logging) {
      return;
    }

    const { phone, code } = this.data;

    if (!validatePhone(phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none',
      });
      return;
    }

    if (code.length < 4) {
      wx.showToast({
        title: '请输入正确的验证码',
        icon: 'none',
      });
      return;
    }

    this.setData({ logging: true });

    try {
      wx.showLoading({ title: '登录中...' });
      
      const response = await authApi.login(phone, code);
      const { token, refresh_token, user } = response.data;
      
      // 保存登录状态
      const store = useStore();
      store.login(user, token, refresh_token);
      
      // 合并本地购物车到服务器
      await store.mergeCartToServer();
      
      wx.showToast({
        title: '登录成功',
        icon: 'success',
      });
      
      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        this.redirectAfterLogin();
      }, 1500);
      
    } catch (error: any) {
      console.error('Login failed:', error);
      wx.showToast({
        title: error.message || '登录失败，请重试',
        icon: 'none',
      });
    } finally {
      wx.hideLoading();
      this.setData({ logging: false });
    }
  },

  // 登录后重定向
  redirectAfterLogin() {
    const redirectUrl = this.redirectUrl || '/pages/home/<USER>';
    
    if (redirectUrl.startsWith('/pages/home/<USER>')) {
      // 如果是首页，使用switchTab
      wx.switchTab({
        url: redirectUrl,
      });
    } else {
      // 其他页面使用reLaunch，清空页面栈
      wx.reLaunch({
        url: redirectUrl,
      });
    }
  },

  // 微信授权登录
  async onGetPhoneNumber(event: any) {
    console.log('Get phone number:', event);
    
    if (event.detail.errMsg !== 'getPhoneNumber:ok') {
      wx.showToast({
        title: '授权失败',
        icon: 'none',
      });
      return;
    }

    // 这里可以实现微信授权登录逻辑
    // 需要后端支持解密微信返回的加密数据
    wx.showToast({
      title: '功能开发中',
      icon: 'none',
    });
  },

  // 查看用户协议
  onViewUserAgreement() {
    wx.navigateTo({
      url: '/pages/webview/webview?url=https://www.daodaole.com/agreement',
    });
  },

  // 查看隐私政策
  onViewPrivacyPolicy() {
    wx.navigateTo({
      url: '/pages/webview/webview?url=https://www.daodaole.com/privacy',
    });
  },

  // 私有属性
  redirectUrl: '',
});

.profile-page {
  min-height: 100vh;
  background-color: #F7F9FA;
}

/* 未登录状态 */
.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 64rpx 32rpx;
}

.login-avatar {
  width: 160rpx;
  height: 160rpx;
  background-color: #E5E5E5;
  border-radius: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80rpx;
  margin-bottom: 32rpx;
}

.login-text {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 48rpx;
}

.login-btn {
  width: 320rpx;
  height: 80rpx;
  background-color: #2FA85A;
  color: #FFFFFF;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
}

.login-btn::after {
  border: none;
}

/* 已登录状态 */
.profile-content {
  padding-bottom: 32rpx;
}

/* 用户信息卡片 */
.user-card {
  background-color: #FFFFFF;
  margin: 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: #F5F5F5;
  margin-right: 24rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  color: #333333;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.user-role {
  margin-bottom: 12rpx;
}

.role-badge {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #FFFFFF;
}

.role-badge.consumer {
  background-color: #2FA85A;
}

.role-badge.business {
  background-color: #FF6B35;
}

.upgrade-tip {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  background-color: #FFF5F0;
  border-radius: 8rpx;
  border-left: 4rpx solid #FF6B35;
}

.upgrade-text {
  font-size: 24rpx;
  color: #FF6B35;
  flex: 1;
}

.upgrade-arrow {
  font-size: 32rpx;
  color: #FF6B35;
}

/* 营业执照状态 */
.license-status {
  border-top: 1rpx solid #F5F5F5;
  padding-top: 24rpx;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.status-label {
  font-size: 28rpx;
  color: #666666;
}

.status-value {
  font-size: 28rpx;
  font-weight: 500;
}

.status-value.uploaded {
  color: #FFA726;
}

.status-value.under_review {
  color: #2196F3;
}

.status-value.approved {
  color: #2FA85A;
}

.status-value.rejected {
  color: #FF4757;
}

.reject-reason {
  font-size: 24rpx;
  color: #FF4757;
  background-color: #FFF5F5;
  padding: 12rpx;
  border-radius: 8rpx;
  margin-top: 12rpx;
}

/* 订单统计 */
.order-stats {
  background-color: #FFFFFF;
  margin: 0 32rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
}

.stats-title {
  font-size: 36rpx;
  color: #333333;
  font-weight: 600;
  margin-bottom: 24rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.stats-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.stats-text {
  font-size: 24rpx;
  color: #666666;
}

.stats-badge {
  position: absolute;
  top: -8rpx;
  right: 20rpx;
  background-color: #FF4757;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
}

/* 功能菜单 */
.menu-section {
  margin: 0 32rpx;
}

.menu-group {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 40rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  color: #333333;
}

.menu-arrow {
  font-size: 32rpx;
  color: #CCCCCC;
}

/* 退出登录 */
.logout-section {
  margin: 32rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background-color: #FFFFFF;
  color: #FF4757;
  border: 2rpx solid #FF4757;
  border-radius: 8rpx;
  font-size: 32rpx;
}

.logout-btn::after {
  border: none;
}

/* 客服联系 */
.contact-section {
  background-color: #FFFFFF;
  margin: 0 32rpx 32rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.contact-text {
  font-size: 28rpx;
  color: #666666;
}

/* 点击效果 */
.menu-item:active,
.stats-item:active,
.contact-item:active {
  background-color: #F5F5F5;
}

.upgrade-tip:active {
  background-color: #FFE8D6;
}
